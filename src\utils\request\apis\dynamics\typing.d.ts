/**
 * 律师动态模块类型声明
 * 基于 api.json 接口文档定义
 */

// 引用通用类型
/// <reference path="../../common-types.d.ts" />

declare namespace DynamicsAPI {
  // 律师动态详情信息类型（基于 LawDynamicsDetailDto）
  interface DynamicsDetailInfo extends CommonTypes.BaseEntity {
    title: string
    content: string  // HTML/富文本
    categoryId: number
    categoryName: string
    viewCount: number
    createdAt: string
  }

  // 律师动态列表信息类型（基于 LawDynamicsListDto）
  interface DynamicsListInfo extends CommonTypes.BaseEntity {
    title: string
    categoryId: number
    categoryName: string
    viewCount: number
    createdAt: string
  }

  // 律师动态分类类型（基于 LawDynamicsCategoryDto）
  interface DynamicsCategoryInfo extends CommonTypes.CategoryDto {}

  // 律师动态分类统计类型（基于 LawyerDynamicsCategoryCountDto）
  interface LawyerDynamicsCategoryCountDto {
    id: number
    name: string
    num: number
  }

  // 获取律师动态详情请求参数（基于 FindDynamicsDetailReq）
  interface GetDynamicsDetailRequest {
    dynamicsId: number
  }

  // 获取律师动态详情响应（基于 FindDynamicsDetailRes）
  interface GetDynamicsDetailResponse {
    detail: DynamicsDetailInfo
    lawyerInfo: LawyerAPI.LawyerInfo
  }

  // 获取动态列表请求参数（基于 FindLawDynamicsListReq）
  interface GetDynamicsListRequest extends CommonTypes.BaseSearchRequest {
    title?: string  // 动态标题模糊搜索
    categoryId?: number  // 分类ID
  }

  // 获取动态列表响应（基于 FindLawDynamicsListRes）
  interface GetDynamicsListResponse extends CommonTypes.PaginationResponse<DynamicsListInfo> {
    LawyerCaseCategoryNum?: LawyerDynamicsCategoryCountDto
  }

  // 获取律师动态列表请求参数（基于 DynamicsListReq）
  interface GetLawyerDynamicsListRequest extends CommonTypes.BaseSearchRequest {
    lawyerId: number
    title?: string  // 动态标题模糊搜索
    categoryId?: number  // 分类ID
  }

  // 获取律师动态列表响应（基于 DynamicsListRes）
  interface GetLawyerDynamicsListResponse extends CommonTypes.PaginationResponse<DynamicsListInfo> {}

  // 获取推荐动态列表请求参数（基于 RecommendListReq）
  interface GetRecommendDynamicsListRequest extends CommonTypes.BaseSearchRequest {
    dynamicsId: number
  }

  // 获取推荐动态列表响应（基于 RecommendListRes）
  interface GetRecommendDynamicsListResponse extends CommonTypes.PaginationResponse<DynamicsListInfo> {}

  // 获取动态分类列表响应（基于 DynamicsCategoryListRes）
  interface GetDynamicsCategoryListResponse {
    list: DynamicsCategoryInfo[]
  }

  // 律师个人中心动态列表信息类型（基于 OwnerCenterDynamicsListDto）
  interface OwnerCenterDynamicsListInfo extends CommonTypes.BaseEntity {
    title: string
    categoryId: number
    categoryName: string
    viewCount: number
    createdAt: string
    status: number  // 状态：1待审核，2已发布，3已拒绝
  }

  // 获取律师个人中心动态列表请求参数（基于 LawyerDynamicsListReq）
  interface GetOwnerCenterDynamicsListRequest extends CommonTypes.BaseSearchRequest {
    title?: string  // 动态标题模糊搜索
    categoryId?: number  // 分类ID
  }

  // 获取律师个人中心动态列表响应（基于 LawyerDynamicsListRes）
  interface GetOwnerCenterDynamicsListResponse extends CommonTypes.PaginationResponse<OwnerCenterDynamicsListInfo> {
    LawyerCaseCategoryNum?: LawyerDynamicsCategoryCountDto
  }

  // 文章收藏请求参数（基于 LawArticleCollectReq）
  interface CollectArticleRequest {
    articleId: number
  }

  // 文章收藏响应（基于 LawArticleCollectRes）
  interface CollectArticleResponse {}

  // 文章点赞请求参数（基于 LawArticleNiceReq）
  interface LikeArticleRequest {
    articleId: number
  }

  // 文章点赞响应（基于 LawArticleNiceRes）
  interface LikeArticleResponse {}

  // 使用通用类型别名
  type PaginationResponse<T> = CommonTypes.PaginationResponse<T>
  type ListResponse<T> = CommonTypes.ListResponse<T>
}
