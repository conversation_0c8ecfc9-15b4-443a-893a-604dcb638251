/**
 * 律师模块类型声明
 * 基于 api.json 接口文档定义
 */

// 引用通用类型
/// <reference path="../../common-types.d.ts" />

declare namespace LawyerAPI {
  // 律师等级类型
  type LawyerLevel = 0 | 1 | 2 | 3 | 4 | 5  //0无数据 1积分律师，2协办律师，3品牌律师，4金牌律师，5金牌大律师

  // 律师擅长领域类型
  interface LawyerFieldDto extends CommonTypes.LawyerFieldDto {}

  // 律师信息类型（基于 FindLawyerListDto）
  interface LawyerInfo extends CommonTypes.BaseEntity {
    userId: number
    province: string
    city: string
    district: string
    name: string
    personalProfile: string  // 个人简介
    figurePhotoUrl: string   // 形象照地址
    lawFirm: string         // 律所名称
    lawFirmAddress: string  // 律所地址
    lawyerLevel: LawyerLevel
    lawyerField: LawyerFieldDto[]  // 律师擅长领域
  }

  // 律师统计数据类型
  interface LawyerCountDataDto extends CommonTypes.LawyerCountDataDto {}

  // 内容统计类型
  interface ContentNumDto extends CommonTypes.ContentNumDto {}

  // 获取律师列表请求参数
  interface GetLawyerListRequest extends CommonTypes.BaseSearchRequest {
    lawyerName?: string    // 律师姓名模糊搜索
    lawyerLevel?: LawyerLevel  // 律师等级
    province?: string      // 所在省份
    city?: string         // 所在城市
    district?: string     // 所在地区
    categoryId?: number   // 案件罪名分类ID
  }

  // 获取律师列表响应
  interface GetLawyerListResponse extends CommonTypes.PaginationResponse<LawyerInfo> {}

  // 获取律师详情请求参数
  interface GetLawyerDetailRequest {
    lawyerId: number  // 律师ID，取值于列表中的user_id
  }

  // 获取律师详情响应
  interface GetLawyerDetailResponse {
    lawyerInfo: LawyerInfo
    lawCase: CaseAPI.CaseListInfo[]      // 个人代表案例
    lawArticle: ArticleAPI.ArticleListInfo[]  // 个人代表著作
    lawyerCountData: LawyerCountDataDto   // 律师统计数据
    contentNum: ContentNumDto             // 个人内容统计
  }

  // 获取律师详情信息请求参数（基于 DetailInfoReq）
  interface GetLawyerDetailInfoRequest {
    lawyerId: number
  }

  // 获取律师详情信息响应（基于 DetailInfoRes）
  interface GetLawyerDetailInfoResponse extends LawyerInfo {}

  // 获取律师统计数据请求参数（基于 DetailLawyerCountReq）
  interface GetLawyerCountDataRequest {
    lawyerId: number
  }

  // 获取律师统计数据响应（基于 DetailLawyerCountRes）
  interface GetLawyerCountDataResponse extends LawyerCountDataDto {}

  // 获取律师内容统计请求参数（基于 DetailContentNumReq）
  interface GetLawyerContentNumRequest {
    lawyerId: number
  }

  // 获取律师内容统计响应（基于 DetailContentNumRes）
  interface GetLawyerContentNumResponse extends ContentNumDto {}

  // 关注律师请求参数（基于 FollowLawyerReq）
  interface FollowLawyerRequest {
    lawyerId: number
  }

  // 关注律师响应（基于 FollowLawyerRes）
  interface FollowLawyerResponse {}

  // 律师关注列表信息类型（基于 OwnerLawyerFollowListDto）
  interface OwnerLawyerFollowListInfo extends CommonTypes.BaseEntity {
    userId: number
    name: string
    figurePhotoUrl: string
    lawyerLevel: LawyerLevel
  }

  // 获取律师关注列表请求参数（基于 OwnerLawyerFollowListReq）
  interface GetOwnerLawyerFollowListRequest extends CommonTypes.BaseSearchRequest {}

  // 获取律师关注列表响应（基于 OwnerLawyerFollowListRes）
  interface GetOwnerLawyerFollowListResponse extends CommonTypes.PaginationResponse<OwnerLawyerFollowListInfo> {}

  // 获取律师个人信息响应（基于 OwnerLawyerInfoListRes）
  interface GetOwnerLawyerInfoResponse {
    userId: number
    province: string
    city: string
    district: string
    name: string
    personalProfile: string
    figurePhotoUrl: string
    lawFirm: string
    lawFirmAddress: string
    authStatus: number
    lawyerLevel: LawyerLevel
    fieldIdStr: string
  }

  // 使用通用类型别名
  type PaginationResponse<T> = CommonTypes.PaginationResponse<T>
  type ListResponse<T> = CommonTypes.ListResponse<T>
}
