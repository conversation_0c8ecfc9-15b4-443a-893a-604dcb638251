/**
 * 请求库主文件
 */
import Taro from '@tarojs/taro'
import { 
  RequestConfig, 
  BaseResponse, 
  UploadConfig, 
  DownloadConfig,
  RequestError 
} from './types'
import { REQUEST_CONFIG, DEFAULT_HEADERS, LOADING_METHODS } from './config'
import { requestInterceptor, responseInterceptor, errorInterceptor } from './interceptors'

class Request {
  private baseURL: string
  private timeout: number
  private defaultHeaders: Record<string, string>

  constructor() {
    this.baseURL = REQUEST_CONFIG.baseURL
    this.timeout = REQUEST_CONFIG.timeout
    this.defaultHeaders = DEFAULT_HEADERS
  }

  /**
   * 显示loading
   */
  private showLoading(text: string = '加载中...') {
    Taro.showLoading({
      title: text,
      mask: true
    })
  }

  /**
   * 隐藏loading
   */
  private hideLoading() {
    Taro.hideLoading()
  }

  /**
   * 显示错误提示
   */
  private showError(message: string) {
    Taro.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  }

  /**
   * 构建完整URL
   */
  private buildURL(url: string): string {
    if (url.startsWith('http')) {
      return url
    }
    return this.baseURL + url
  }

  /**
   * 核心请求方法
   */
  async request<T = any>(config: RequestConfig): Promise<BaseResponse<T>> {
    try {
      // 请求拦截
      const requestConfig = requestInterceptor(config)
      
      // 显示loading
      if (requestConfig.showLoading !== false && 
          LOADING_METHODS.includes(requestConfig.method || 'GET')) {
        this.showLoading(requestConfig.loadingText)
      }

      // 发起请求
      const method = requestConfig.method || 'GET'
      const response = await Taro.request({
        url: this.buildURL(requestConfig.url),
        method: method as any, // 类型断言以支持扩展的HTTP方法如PATCH
        data: requestConfig.data,
        header: {
          ...this.defaultHeaders,
          ...requestConfig.header
        },
        timeout: requestConfig.timeout || this.timeout
      })

      // 隐藏loading
      this.hideLoading()

      // 响应拦截
      const result = responseInterceptor<T>(response.data)
      return result

    } catch (error) {
      // 隐藏loading
      this.hideLoading()

      // 错误拦截
      const requestError: RequestError = errorInterceptor(error)

      // 显示错误提示
      if (config.showError !== false) {
        this.showError(requestError.message)
      }

      throw requestError
    }
  }

  /**
   * GET请求
   */
  get<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<BaseResponse<T>> {
    return this.request<T>({
      url,
      method: 'GET',
      data,
      ...config
    })
  }

  /**
   * POST请求
   */
  post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<BaseResponse<T>> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      ...config
    })
  }

  /**
   * PUT请求
   */
  put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<BaseResponse<T>> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      ...config
    })
  }

  /**
   * DELETE请求
   */
  delete<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<BaseResponse<T>> {
    return this.request<T>({
      url,
      method: 'DELETE',
      data,
      ...config
    })
  }

  /**
   * PATCH请求
   */
  patch<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<BaseResponse<T>> {
    return this.request<T>({
      url,
      method: 'PATCH',
      data,
      ...config
    })
  }

  /**
   * 上传文件
   */
  async upload<T = any>(config: UploadConfig): Promise<BaseResponse<T>> {
    try {
      // 显示loading
      if (config.showLoading !== false) {
        this.showLoading(config.loadingText || '上传中...')
      }

      const response = await Taro.uploadFile({
        url: this.buildURL(config.url),
        filePath: config.filePath,
        name: config.name,
        formData: config.formData,
        header: {
          ...this.defaultHeaders,
          ...config.header
        }
      })

      // 隐藏loading
      this.hideLoading()

      // 解析响应数据
      const result = JSON.parse(response.data)
      return responseInterceptor<T>(result)

    } catch (error) {
      this.hideLoading()
      const requestError: RequestError = errorInterceptor(error)
      this.showError(requestError.message)
      throw requestError
    }
  }

  /**
   * 下载文件
   */
  async download(config: DownloadConfig): Promise<string> {
    try {
      // 显示loading
      if (config.showLoading !== false) {
        this.showLoading(config.loadingText || '下载中...')
      }

      const response = await Taro.downloadFile({
        url: this.buildURL(config.url),
        header: {
          ...this.defaultHeaders,
          ...config.header
        }
      })

      // 隐藏loading
      this.hideLoading()

      if (response.statusCode === 200) {
        return response.tempFilePath
      } else {
        throw new Error('下载失败')
      }

    } catch (error) {
      this.hideLoading()
      const requestError: RequestError = errorInterceptor(error)
      this.showError(requestError.message)
      throw requestError
    }
  }
}

// 创建请求实例
const request = new Request()

export default request
export { Request }
export * from './types'
