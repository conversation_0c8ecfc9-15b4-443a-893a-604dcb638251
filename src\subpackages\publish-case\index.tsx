/**
 * 案例发布/编辑页面
 */
import React, { useState, useEffect } from 'react'
import { <PERSON>, Textare<PERSON>, Picker, But<PERSON> } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import PageLayout from '@/components/PageLayout'
import { caseApi } from '@/utils/request/apis/case'
import './index.scss'

const PublishCase: React.FC = () => {
  const router = useRouter()
  const { id } = router.params // 案例ID，编辑时传入

  // 页面状态
  const [isEdit, setIsEdit] = useState(false) // 是否为编辑模式
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)

  // 表单数据
  const [formData, setFormData] = useState({
    title: '', // 案例标题
    categoryId: 0, // 分类ID
    content: '' // 案例内容
  })

  // 分类数据
  const [categories, setCategories] = useState<CaseAPI.CaseCategoryInfo[]>([])
  const [selectedCategoryIndex, setSelectedCategoryIndex] = useState(0)

  // 字数统计
  const [titleCount, setTitleCount] = useState(0)
  const [contentCount, setContentCount] = useState(0)
  const maxTitleWords = 100
  const maxContentWords = 5000

  // 计算按钮是否可用
  const isButtonDisabled = submitting || !formData.title.trim() || !formData.content.trim() || !formData.categoryId

  // 获取案例分类列表
  const loadCategories = async () => {
    try {
      const response = await caseApi.getCaseCategoryList()
      if (response.success && response.data) {
        setCategories(response.data.list)
        if (response.data.list.length > 0 && !isEdit) {
          setFormData(prev => ({
            ...prev,
            categoryId: response.data.list[0].id
          }))
        }
      }
    } catch (error) {
      console.error('获取分类列表失败:', error)
      // 使用模拟数据作为后备
      const mockCategories = [
        { id: 1, name: '民事案例' },
        { id: 2, name: '刑事案例' },
        { id: 3, name: '商事案例' },
        { id: 4, name: '行政案例' }
      ]
      setCategories(mockCategories)
      if (!isEdit) {
        setFormData(prev => ({
          ...prev,
          categoryId: mockCategories[0].id
        }))
      }
      Taro.showToast({
        title: '使用默认分类',
        icon: 'none'
      })
    }
  }

  // 获取案例详情（编辑模式）
  const loadCaseDetail = async (caseId: number) => {
    try {
      setLoading(true)
      const response = await caseApi.getCaseDetail(caseId)
      if (response.success && response.data) {
        const detail = response.data.detail
        setFormData({
          title: detail.title,
          categoryId: detail.categoryId,
          content: detail.content
        })
        setTitleCount(detail.title.length)
        setContentCount(detail.content.length)

        // 设置分类选择器的索引
        const categoryIndex = categories.findIndex(cat => cat.id === detail.categoryId)
        if (categoryIndex !== -1) {
          setSelectedCategoryIndex(categoryIndex)
        }
      }
    } catch (error) {
      console.error('获取案例详情失败:', error)
      Taro.showToast({
        title: '获取案例失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  // 处理标题输入
  const handleTitleChange = (e: any) => {
    const title = e.detail.value
    if (title.length <= maxTitleWords) {
      setFormData(prev => ({
        ...prev,
        title: title
      }))
      setTitleCount(title.length)
    }
  }

  // 处理内容输入
  const handleContentChange = (e: any) => {
    const content = e.detail.value
    if (content.length <= maxContentWords) {
      setFormData(prev => ({
        ...prev,
        content: content
      }))
      setContentCount(content.length)
    }
  }

  // 处理分类选择
  const handleCategoryChange = (e: any) => {
    const index = parseInt(e.detail.value)
    setSelectedCategoryIndex(index)
    if (categories[index]) {
      setFormData(prev => ({
        ...prev,
        categoryId: categories[index].id
      }))
    }
  }

  // 表单验证
  const validateForm = (): boolean => {
    if (!formData.title.trim()) {
      Taro.showToast({
        title: '请输入案例标题',
        icon: 'none'
      })
      return false
    }

    if (!formData.content.trim()) {
      Taro.showToast({
        title: '请输入案例内容',
        icon: 'none'
      })
      return false
    }

    if (!formData.categoryId) {
      Taro.showToast({
        title: '请选择案例分类',
        icon: 'none'
      })
      return false
    }

    return true
  }

  // 提交发布/更新
  const handleSubmit = async () => {
    // 表单验证
    if (!validateForm()) {
      return
    }

    try {
      setSubmitting(true)

      if (isEdit && id) {
        // 更新案例
        const response = await caseApi.updateCase({
          id: parseInt(id),
          title: formData.title.trim(),
          categoryId: formData.categoryId,
          content: formData.content.trim()
        })

        if (response.success) {
          Taro.showToast({
            title: '更新成功',
            icon: 'success'
          })
        } else {
          throw new Error(response.message || '更新失败')
        }
      } else {
        // 创建案例
        const response = await caseApi.createCase({
          title: formData.title.trim(),
          categoryId: formData.categoryId,
          content: formData.content.trim()
        })

        if (response.success) {
          Taro.showToast({
            title: '发布成功',
            icon: 'success'
          })
        } else {
          throw new Error(response.message || '发布失败')
        }
      }

      // 返回上一页
      setTimeout(() => {
        Taro.navigateBack()
      }, 1500)

    } catch (error) {
      console.error('提交失败:', error)
      Taro.showToast({
        title: isEdit ? '更新失败' : '发布失败',
        icon: 'none'
      })
    } finally {
      setSubmitting(false)
    }
  }

  // 页面初始化
  useEffect(() => {
    if (id) {
      setIsEdit(true)
    }
    loadCategories()
  }, [])

  // 编辑模式下加载案例详情
  useEffect(() => {
    if (isEdit && id && categories.length > 0) {
      loadCaseDetail(parseInt(id))
    }
  }, [isEdit, id, categories])

  return (
    <PageLayout
      title={isEdit ? '编辑案例' : '发布案例'}
      showBack
      backgroundColor='#f8f9fa'
    >
      <View className='publish-case'>
        {loading ? (
          <View className='loading-container'>
            <View className='loading-text'>加载中...</View>
          </View>
        ) : (
          <>
            <View className='content-area'>
              {/* 分类选择 */}
              <View className='form-section'>
                <View className='form-label'>案例分类</View>
                <Picker
                  mode='selector'
                  range={categories.map(cat => cat.name)}
                  value={selectedCategoryIndex}
                  onChange={handleCategoryChange}
                >
                  <View className='category-picker'>
                    <View className='picker-text'>
                      {categories[selectedCategoryIndex]?.name || '请选择分类'}
                    </View>
                    <View className='picker-arrow'>›</View>
                  </View>
                </Picker>
              </View>

              {/* 标题输入 */}
              <View className='form-section'>
                <View className='form-label'>
                  案例标题
                  <View className='word-count'>
                    {titleCount}/{maxTitleWords}
                  </View>
                </View>
                <Textarea
                  className='title-textarea'
                  placeholder='请输入案例标题...'
                  value={formData.title}
                  onInput={handleTitleChange}
                  maxlength={maxTitleWords}
                  showConfirmBar={false}
                  adjustPosition={false}
                  autoHeight={false}
                  fixed
                />
              </View>

              {/* 内容输入 */}
              <View className='form-section'>
                <View className='form-label'>
                  案例内容
                  <View className='word-count'>
                    {contentCount}/{maxContentWords}
                  </View>
                </View>
                <Textarea
                  className='content-textarea'
                  placeholder='请详细描述案例背景、争议焦点、处理过程、判决结果等...'
                  value={formData.content}
                  onInput={handleContentChange}
                  maxlength={maxContentWords}
                  showConfirmBar={false}
                  adjustPosition={false}
                  autoHeight={false}
                  fixed
                />
              </View>
            </View>

            {/* 底部提交按钮 */}
            <View className='submit-section'>
              <Button
                className='submit-btn'
                onClick={handleSubmit}
                disabled={isButtonDisabled}
                loading={submitting}
                type='primary'
                size='default'
                formType='submit'
                hoverClass={isButtonDisabled ? 'none' : 'button-hover'}
                hoverStayTime={100}
              >
                {submitting ? '提交中...' : (isEdit ? '更新案例' : '发布案例')}
              </Button>
            </View>
          </>
        )}
      </View>
    </PageLayout>
  )
}

export default PublishCase
