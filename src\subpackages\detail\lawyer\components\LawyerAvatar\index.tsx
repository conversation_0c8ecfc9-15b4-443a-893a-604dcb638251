/**
 * 律师头像组件
 * 
 * 展示律师头像、认证图标、等级图标、姓名、关注按钮和个人简介
 */
import React from 'react'
import { View, Text, Image } from '@tarojs/components'
import './index.scss'

// 导入图标
import verifyIcon from '@/assets/images/common-icon/verify.png'
import focusIcon from '@/assets/images/common-icon/focus.png'
import heartOutlineIcon from '@/assets/images/common-icon/heart_outline_w.png'
import heartActiveIcon from '@/assets/images/common-icon/heart_active.png'

// 导入律师等级图标
import layerLevel1Icon from '@/assets/images/layer-level-icon/layer_1.png'
import layerLevel2Icon from '@/assets/images/layer-level-icon/layer_2.png'
import layerLevel3Icon from '@/assets/images/layer-level-icon/layer_3.png'
import layerLevel4Icon from '@/assets/images/layer-level-icon/layer_4.png'
import layerLevel5Icon from '@/assets/images/layer-level-icon/layer_5.png'

// 组件属性
export interface LawyerAvatarProps {
  // 律师信息
  lawyerInfo: LawyerAPI.LawyerInfo
  // 是否已关注
  isFollowed: boolean
  // 关注状态切换回调
  onFollowToggle: () => void
  // 自定义样式类名
  className?: string
}

const LawyerAvatar: React.FC<LawyerAvatarProps> = ({
  lawyerInfo,
  isFollowed,
  onFollowToggle,
  className = ''
}) => {
  // 获取律师等级图标
  const getLevelIcon = (lawyerLevel: number): string => {
    const levelIconMap = {
      1: layerLevel1Icon,
      2: layerLevel2Icon,
      3: layerLevel3Icon,
      4: layerLevel4Icon,
      5: layerLevel5Icon
    }
    return levelIconMap[lawyerLevel] || layerLevel1Icon
  }

  return (
    <View className={`lawyer-avatar ${className}`}>
      <View className='lawyer-avatar__container'>
        <Image
          className='lawyer-avatar__img'
          src={lawyerInfo.figurePhotoUrl}
          mode='aspectFill'
        />

        {/* 右上角认证图标 */}
        <View className='lawyer-avatar__verify'>
          <Image
            className='lawyer-avatar__verify-icon'
            src={verifyIcon}
            mode='aspectFit'
          />
        </View>

        {/* 姓名和关注按钮 */}
        <View className='lawyer-avatar__name-follow'>
          <View className='lawyer-avatar__name-section'>
            <Text className='lawyer-avatar__name-text'>{lawyerInfo.name}</Text>
            {/* 律师等级图标 */}
            <Image
              className='lawyer-avatar__level-badge-icon'
              src={getLevelIcon(lawyerInfo.lawyerLevel)}
              mode='left'
            />
          </View>
          <View className='lawyer-avatar__follow-section'>
            <Image
              className='lawyer-avatar__follow-icon'
              src={isFollowed ? heartActiveIcon : heartOutlineIcon}
              mode='aspectFit'
              onClick={onFollowToggle}
            />
            <Text className='lawyer-avatar__follow-text'>
              {isFollowed ? '已关注' : '关注'}
            </Text>
          </View>
        </View>

        {/* 聚焦信息框 */}
        <View className='lawyer-avatar__focus'>
          <Image
            className='lawyer-avatar__focus-bg'
            src={focusIcon}
            mode='aspectFit'
          />
          <Text className='lawyer-avatar__focus-text'>{lawyerInfo.personalProfile}</Text>
        </View>
      </View>
    </View>
  )
}

export default LawyerAvatar
