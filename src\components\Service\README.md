# Service 客服组件

## 功能特性

- 🎯 **固定定位**：漂浮在页面右下角，不影响页面布局
- 🎨 **圆形设计**：88rpx × 88rpx 圆形按钮，简洁美观
- 📞 **微信客服**：点击直接跳转到微信客服对话
- 🔄 **备用方案**：客服不可用时提供电话联系方式
- 📱 **响应式交互**：支持点击缩放动画效果
- 🎛️ **可控显隐**：支持动态控制组件显示/隐藏

## 使用方法

### 基础用法

```tsx
import Service from '@/components/Service'

// 基础使用
<Service />
```

### 控制显隐

```tsx
import Service from '@/components/Service'
import { useState } from 'react'

const MyPage = () => {
  const [showService, setShowService] = useState(true)

  return (
    <View>
      {/* 页面内容 */}
      
      {/* 客服组件 */}
      <Service visible={showService} />
    </View>
  )
}
```

### 自定义样式

```tsx
import Service from '@/components/Service'

<Service 
  className="custom-service"
  visible={true}
/>
```

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| className | `string` | `''` | 自定义样式类名 |
| visible | `boolean` | `true` | 是否显示组件 |

## 样式规格

### 组件尺寸
- **宽度**：88rpx
- **高度**：88rpx
- **形状**：圆形（border-radius: 50%）
- **背景**：#fff（白色）

### 定位信息
- **位置**：fixed 固定定位
- **右边距**：25rpx
- **底边距**：65rpx
- **层级**：z-index: 999

### 图标规格
- **尺寸**：40rpx × 40rpx
- **图片**：headset_outline.png
- **模式**：aspectFit

### 文字规格
- **内容**：客服
- **字重**：600
- **字号**：16rpx
- **颜色**：#000000
- **行高**：18rpx
- **对齐**：居中

### 间距规格
- **图标与文字间距**：5rpx

## 交互效果

### 点击效果
- **缩放**：点击时缩放到 0.95
- **阴影**：点击时阴影变化
- **过渡**：0.3s ease 过渡动画

### 悬停效果（H5）
- **阴影增强**：悬停时阴影加深

## 功能说明

### 微信客服跳转
组件使用 Taro 的 `openCustomerServiceChat` API 实现微信客服功能：

```typescript
Taro.openCustomerServiceChat({
  extInfo: {
    url: 'https://work.weixin.qq.com/kfid/kfc123456789'
  },
  corpId: 'your_corp_id',
  success: (res) => {
    console.log('打开客服成功:', res)
  },
  fail: (err) => {
    console.error('打开客服失败:', err)
  }
})
```

### 备用方案
当微信客服不可用时，组件会提供备用方案：
1. 显示提示信息
2. 提供电话客服选项
3. 支持一键拨打电话

## 配置说明

### 客服配置
使用前需要配置以下信息：

1. **客服链接**：替换 `extInfo.url` 中的客服链接
2. **企业ID**：替换 `corpId` 为实际的企业微信ID
3. **客服电话**：替换备用方案中的电话号码

### 示例配置

```typescript
// 在组件中修改以下配置
const serviceConfig = {
  // 微信客服链接
  serviceUrl: 'https://work.weixin.qq.com/kfid/your_kf_id',
  // 企业微信ID
  corpId: 'your_actual_corp_id',
  // 备用客服电话
  phoneNumber: '************'
}
```

## 注意事项

1. **权限要求**：需要在微信小程序后台配置客服功能
2. **平台兼容**：主要适用于微信小程序，其他平台会使用备用方案
3. **图标资源**：确保 `headset_outline.png` 图标文件存在
4. **层级管理**：组件使用 z-index: 999，注意与其他浮层的层级关系
5. **性能考虑**：组件使用 fixed 定位，不会影响页面滚动性能

## 自定义样式示例

```scss
// 自定义客服组件样式
.custom-service {
  // 修改背景色
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  // 修改文字颜色
  .service-widget__text {
    color: #fff;
  }
  
  // 修改阴影
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
}
```

## 使用场景

- **全局客服**：在 app.tsx 中全局使用
- **页面客服**：在特定页面中使用
- **条件显示**：根据用户状态控制显示
- **多端适配**：配合不同平台的客服方案
