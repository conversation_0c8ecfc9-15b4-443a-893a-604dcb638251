/**
 * 律师动态列表组件
 * 
 * 展示最多5个动态卡片，支持横向滑动，支持外部传入筛选条件
 */
import React, { useState, useEffect } from 'react'
import { View, ScrollView, Text } from '@tarojs/components'
import { dynamicsApi } from '@/utils/request/apis'
import { navigateToPage } from '@/utils'
import { DynamicsListProps } from './types'
import './index.scss'

const DynamicsList: React.FC<DynamicsListProps> = ({
  filterParams,
  className = ''
}) => {
  // 动态列表数据
  const [dynamics, setDynamics] = useState<DynamicsAPI.DynamicsListInfo[]>([])
  // 加载状态
  const [isLoading, setIsLoading] = useState(false)

  // 格式化时间
  const formatTime = (timeStr: string): string => {
    const date = new Date(timeStr)
    return `${date.getMonth() + 1}-${String(date.getDate()).padStart(2, '0')}`
  }

  // 加载动态列表数据
  const loadDynamics = async () => {
    try {
      setIsLoading(true)
      const params = {
        ...filterParams,
        page: 1,
        pageSize: 5 // 最多展示5个动态
      }

      const response = await dynamicsApi.getDynamicsList(params)
      if (response.success && response.data) {
        setDynamics(response.data.list)
      }
    } catch (error) {
      // 模拟数据作为fallback
      const mockList: DynamicsAPI.DynamicsListInfo[] = [
        {
          id: 1,
          title: "律师行业数字化转型趋势分析",
          categoryId: 1,
          categoryName: "行业动态",
          viewCount: 856,
          createdAt: "2025-06-22 09:15:00"
        },
        {
          id: 2, 
          title: "新《民法典》实施后的法律实务变化",
          categoryId: 2,
          categoryName: "法律解读",
          viewCount: 1203,
          createdAt: "2025-06-21 14:30:00"
        },
        {
          id: 3,
          title: "知识产权保护的最新司法解释",
          categoryId: 3,
          categoryName: "政策解读",
          viewCount: 678,
          createdAt: "2025-06-20 16:45:00"
        },
        {
          id: 4,
          title: "企业合规管理的重要性与实践",
          categoryId: 4,
          categoryName: "企业法务",
          viewCount: 945,
          createdAt: "2025-06-19 11:20:00"
        },
        {
          id: 5,
          title: "律师职业道德与执业规范探讨",
          categoryId: 5,
          categoryName: "职业发展",
          viewCount: 567,
          createdAt: "2025-06-18 08:30:00"
        }
      ]
      setDynamics(mockList)
      console.error('加载动态列表失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 处理动态卡片点击 - 跳转到动态详情页
  const handleDynamicsClick = (dynamicsInfo: DynamicsAPI.DynamicsListInfo) => {
    navigateToPage(`/subpackages/detail/dynamics/index?id=${dynamicsInfo.id}`)
  }

  // 组件挂载时加载数据
  useEffect(() => {
    loadDynamics()
  }, [filterParams])

  // 显示加载状态
  const showLoading = isLoading

  return (
    <View className={`dynamics-list ${className}`}>
      {showLoading ? (
        <View className='dynamics-list__loading'>
          <Text>加载中...</Text>
        </View>
      ) : (
        <>
          {dynamics.length > 0 ? (
            <ScrollView
              className='dynamics-list__scroll-view'
              scrollX
              showScrollbar={false}
              enhanced
              bounces={false}
            >
              <View className='dynamics-list__container'>
                {dynamics.map((dynamicsInfo) => (
                  <View
                    key={dynamicsInfo.id}
                    className='dynamics-card'
                    onClick={() => handleDynamicsClick(dynamicsInfo)}
                  >
                    <Text className='dynamics-card__title'>{dynamicsInfo.title}</Text>
                    <View className='dynamics-card__meta'>
                      <Text className='dynamics-card__category'>{dynamicsInfo.categoryName}</Text>
                      <View className='dynamics-card__info'>
                        <Text className='dynamics-card__time'>{formatTime(dynamicsInfo.createdAt)}</Text>
                        <Text className='dynamics-card__view-count'>浏览 {dynamicsInfo.viewCount}</Text>
                      </View>
                    </View>
                  </View>
                ))}
              </View>
            </ScrollView>
          ) : (
            <View className='dynamics-list__empty'>
              <Text className='dynamics-list__empty-title'>暂无动态信息</Text>
              <Text className='dynamics-list__empty-desc'>当前没有相关动态，请稍后再试</Text>
            </View>
          )}
        </>
      )}
    </View>
  )
}

// 导出组件
export default DynamicsList

// 导出类型
export type { DynamicsListProps } from './types'
