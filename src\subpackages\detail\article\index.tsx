/**
 * 文章详情页面
 */
import React, { useState, useEffect } from 'react'
import { View, Text, Button } from '@tarojs/components'
import { useRouter } from '@tarojs/taro'
import { articleApi } from '@/utils/request/apis'
import { PageLayout } from '@/components'
import './index.scss'

interface ArticleDetailInfo {
  id: number
  title: string
  content: string
  categoryId: number
  categoryName: string
  likeCount: number
  viewCount: number
  favoriteCount: number
  createdAt: string
}

const ArticleDetail: React.FC = () => {
  const router = useRouter()
  const { id } = router.params
  
  const [articleInfo, setArticleInfo] = useState<ArticleDetailInfo | null>(null)


  // 格式化时间
  const formatTime = (timeStr: string): string => {
    const date = new Date(timeStr)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  }

  // 加载文章详情
  const loadArticleDetail = async () => {
    try {
      if (id) {
        const response = await articleApi.getArticleDetail(Number(id))
        if (response.success && response.data) {
          setArticleInfo(response.data.detail)
        }
      }
    } catch (error) {
      // 模拟数据
      const mockData: ArticleDetailInfo = {
        id: Number(id) || 1,
        title: '劳动合同纠纷处理指南：员工权益保护实务',
        content: `
          <p>劳动合同纠纷是当前社会中较为常见的法律问题之一。作为劳动者，了解自己的权益和维权途径至关重要。</p>
          
          <h3>一、常见的劳动合同纠纷类型</h3>
          <p>1. 工资拖欠或克扣</p>
          <p>2. 违法解除劳动合同</p>
          <p>3. 加班费争议</p>
          <p>4. 社会保险缴纳问题</p>
          
          <h3>二、维权途径</h3>
          <p>1. 协商解决：首先与用人单位进行协商</p>
          <p>2. 劳动仲裁：向劳动争议仲裁委员会申请仲裁</p>
          <p>3. 法院诉讼：对仲裁结果不服可向法院起诉</p>
          
          <h3>三、注意事项</h3>
          <p>1. 保留相关证据材料</p>
          <p>2. 注意时效期限</p>
          <p>3. 寻求专业法律帮助</p>
        `,
        categoryId: 1,
        categoryName: '劳动法',
        likeCount: 89,
        viewCount: 1250,
        favoriteCount: 45,
        createdAt: '2025-06-20 10:30:00'
      }
      setArticleInfo(mockData)
      console.error('加载文章详情失败:', error)
    } finally {
      // 加载完成
    }
  }

  useEffect(() => {
    loadArticleDetail()
  }, [id])

  return (
    <PageLayout
      title="文章详情"
    >
      {articleInfo && (
        <View className="article-detail__content">
          {/* 文章标题 */}
          <Text className="article-title">{articleInfo.title}</Text>
          
          {/* 文章信息 */}
          <View className="article-meta">
            <View className="article-meta__info">
              <Text className="article-meta__time">{formatTime(articleInfo.createdAt)}</Text>
              <Text className="article-meta__stats">
                阅读 {articleInfo.viewCount} · 点赞 {articleInfo.likeCount} · 收藏 {articleInfo.favoriteCount}
              </Text>
            </View>
          </View>

          {/* 文章分类 */}
          <View className="article-tags">
            <Text className="article-tags__category">{articleInfo.categoryName}</Text>
          </View>

          {/* 文章内容 */}
          <View className="article-content">
            <Text className="article-content__text">{articleInfo.content.replace(/<[^>]*>/g, '')}</Text>
          </View>

          {/* 操作按钮 */}
          <View className="article-actions">
            <Button className="article-actions__btn" size="mini">
              点赞 ({articleInfo.likeCount})
            </Button>
            <Button className="article-actions__btn" size="mini">
              收藏 ({articleInfo.favoriteCount})
            </Button>
            <Button className="article-actions__btn" size="mini">
              分享
            </Button>
          </View>
        </View>
      )}
    </PageLayout>
  )
}

export default ArticleDetail
