/**
 * 律师信息Tab内容组件
 * 
 * 包含律师简介、擅长领域、律师数据、律所信息
 */
import React from 'react'
import { View } from '@tarojs/components'
import LawyerProfile from '../LawyerProfile'
import LawyerFields from '../LawyerFields'
import LawyerStatsCard from '../LawyerStatsCard'
import LawyerFirm from '../LawyerFirm'
import './index.scss'

// 组件属性
export interface LawyerInfoTabProps {
  // 律师信息
  lawyerInfo: LawyerAPI.LawyerInfo
  // 律师统计数据
  lawyerCountData: LawyerAPI.LawyerCountDataDto | null
  // 简介展开状态
  isProfileExpanded: boolean
  // 简介展开切换回调
  onProfileToggle: () => void
  // 是否显示加载状态
  loading?: boolean
  // 自定义样式类名
  className?: string
}

const LawyerInfoTab: React.FC<LawyerInfoTabProps> = ({
  lawyerInfo,
  lawyerCountData,
  isProfileExpanded,
  onProfileToggle,
  loading = false,
  className = '',
}) => {
  return (
    <View className={`lawyer-info-tab ${className}`}>
      {/* 律师简介 */}
      <LawyerProfile
        personalProfile={lawyerInfo.personalProfile}
        isExpanded={isProfileExpanded}
        onToggle={onProfileToggle}
      />

      {/* 擅长领域 */}
      <LawyerFields lawyerField={lawyerInfo.lawyerField} />

      {/* 律师数据 */}
      <LawyerStatsCard
        lawyerCountData={lawyerCountData}
        className='lawyer-info-tab__stats-card'
        loading={loading}
      />

      {/* 律所信息 */}
      <LawyerFirm lawyerInfo={lawyerInfo} />
    </View>
  )
}

export default LawyerInfoTab
