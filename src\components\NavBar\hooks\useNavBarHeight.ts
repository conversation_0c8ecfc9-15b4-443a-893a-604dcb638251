/**
 * 导航栏高度计算 Hook
 *
 * 自动计算适配不同设备的导航栏高度
 *
 * 计算逻辑：
 * 1. 导航栏总高度 = 胶囊按钮底部位置 + 胶囊按钮顶部到状态栏的距离
 * 2. 内容区域高度 = 导航栏总高度 (占据整个导航栏)
 * 3. 通过 paddingTop = 状态栏高度 来避开状态栏区域
 * 4. 实际可用内容高度 = 导航栏总高度 - 状态栏高度
 */
import { useState, useEffect } from 'react'
import Taro from '@tarojs/taro'

interface NavBarHeightResult {
  // 导航栏总高度
  navBarHeight: number
  // 内容区域高度
  contentHeight: number
  // 状态栏高度
  statusBarHeight: number
  // 胶囊按钮信息
  menuButtonInfo: {
    top: number
    height: number
    width: number
    right: number
  }
}

/**
 * 使用导航栏高度计算
 * @returns 导航栏高度相关信息
 */
export const useNavBarHeight = (): NavBarHeightResult => {
  const [heights, setHeights] = useState<NavBarHeightResult>({
    navBarHeight: 0,
    contentHeight: 44,
    statusBarHeight: 44,
    menuButtonInfo: {
      top: 0,
      height: 32,
      width: 87,
      right: 0
    }
  })

  useEffect(() => {
    try {
      // 获取窗口信息
      const windowInfo = Taro.getWindowInfo()
      // 状态栏的高度，单位px
      const statusBarHeight = windowInfo.statusBarHeight || 44
      // 获取胶囊按钮位置信息
      const menuButton = Taro.getMenuButtonBoundingClientRect()
      const { top: buttonTop, height: buttonHeight, width: buttonWidth, right: buttonRight } = menuButton
      // 计算导航栏高度
      // 导航栏高度 = 胶囊按钮底部位置 + 胶囊按钮顶部到状态栏的距离
      const buttonBottom = buttonTop + buttonHeight
      const buttonTopMargin = buttonTop - statusBarHeight
      const calculatedNavHeight = buttonBottom + buttonTopMargin
      // 内容区域高度 = 导航栏总高度
      // 内容区域占据整个导航栏高度，通过 paddingTop 来避开状态栏
      const calculatedContentHeight = calculatedNavHeight

      setHeights({
        navBarHeight: calculatedNavHeight,
        contentHeight: calculatedContentHeight,
        statusBarHeight,
        menuButtonInfo: {
          top: buttonTop,
          height: buttonHeight,
          width: buttonWidth,
          right: buttonRight
        }
      })
    } catch (error) {
      console.warn('获取导航栏高度失败，使用默认值:', error)

      // 使用默认值
      const defaultStatusBarHeight = 44
      const defaultNavBarHeight = 88
      const defaultContentHeight = defaultNavBarHeight // 内容区域占据整个导航栏高度

      setHeights({
        navBarHeight: defaultNavBarHeight,
        contentHeight: defaultContentHeight,
        statusBarHeight: defaultStatusBarHeight,
        menuButtonInfo: {
          top: defaultStatusBarHeight,
          height: 32,
          width: 87,
          right: 365
        }
      })
    }
  }, [])
  return heights
}

/**
 * 获取导航栏样式
 * @param backgroundColor 背景色
 * @param fixed 是否固定定位
 * @returns 导航栏样式对象
 */
export const getNavBarStyle = (
  navBarHeight: number,
  backgroundColor: string = '#ffffff',
  fixed: boolean = true
) => {
  return {
    height: `${navBarHeight}px`,
    backgroundColor,
    position: fixed ? 'fixed' as const : 'relative' as const,
    top: fixed ? 0 : 'auto',
    left: fixed ? 0 : 'auto',
    right: fixed ? 0 : 'auto',
    zIndex: fixed ? 999 : 'auto'
  }
}

/**
 * 获取内容区域样式
 * @param statusBarHeight 状态栏高度 (用于 paddingTop)
 * @param contentHeight 内容高度 (等于导航栏总高度)
 * @returns 内容区域样式对象
 *
 * 说明：
 * - height: contentHeight (导航栏总高度，如 87px)
 * - paddingTop: statusBarHeight (状态栏高度，如 47px)
 * - 实际可用高度: contentHeight - statusBarHeight (如 87-47=40px)
 */
export const getContentStyle = (
  statusBarHeight: number,
  contentHeight: number
) => {
  return {
    paddingTop: `${statusBarHeight}px`,
    height: `${contentHeight}px`,
    boxSizing: 'border-box' as const
  }
}

/**
 * 计算页面内容的 padding-top
 * 当导航栏固定定位时，页面内容需要预留导航栏高度的空间
 * @param navBarHeight 导航栏高度
 * @param fixed 是否固定定位
 * @returns padding-top 值
 */
export const getPageContentPaddingTop = (
  navBarHeight: number,
  fixed: boolean = true
): string => {
  return fixed ? `${navBarHeight}px` : '0px'
}
