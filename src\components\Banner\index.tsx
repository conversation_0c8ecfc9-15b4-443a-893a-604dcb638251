/**
 * Banner 轮播组件
 * 
 * 基于 Taro Swiper 组件封装的轮播图组件
 * 支持图片轮播、自动播放、点击跳转等功能
 */
import React, { useState, useCallback } from 'react'
import { View, Swiper, SwiperItem, Image } from '@tarojs/components'
import { navigateToPage } from '@/utils'
import { BannerProps, BannerItem } from './types'
import './index.scss'

const Banner: React.FC<BannerProps> = ({
  banners = [],
  autoplay = true,
  interval = 3000,
  showIndicators = true,
  showDots = true,
  indicatorColor = 'rgba(255, 255, 255, 0.5)',
  indicatorActiveColor = '#ffffff',
  circular = true,
  duration = 300,
  fallbackImage = '/assets/images/banner-placeholder.png',
  className = '',
  onClick,
  onChange
}) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [imageErrors, setImageErrors] = useState<Set<number>>(new Set())

  // 处理轮播切换
  const handleSwiperChange = useCallback((e: any) => {
    const { current } = e.detail
    setCurrentIndex(current)
    onChange?.(current)
  }, [onChange])

  // 处理图片加载错误
  const handleImageError = useCallback((index: number) => {
    setImageErrors(prev => new Set(prev).add(index))
  }, [])

  // 处理点击事件
  const handleItemClick = useCallback(async (item: BannerItem, index: number) => {
    // 如果有自定义点击事件，优先执行
    if (onClick) {
      onClick(item, index)
      return
    }

    // 如果没有跳转链接，不执行任何操作
    if (!item.linkUrl) {
      return
    }

    // 使用统一的页面跳转方法
    try {
      await navigateToPage(item.linkUrl, {
        showToast: true,
        toastTitle: 'Banner跳转失败'
      })
    } catch (error) {
      console.error('Banner 跳转失败:', error)
      // navigateToPage 已经处理了错误提示，这里只记录日志
    }
  }, [onClick])

  // 如果没有数据，不渲染
  if (!banners || banners.length === 0) {
    return null
  }

  return (
    <View className={`banner ${className}`}>
      <Swiper
        className="banner__swiper"
        autoplay={autoplay}
        interval={interval}
        circular={circular}
        duration={duration}
        indicatorDots={showDots}
        indicatorColor={indicatorColor}
        indicatorActiveColor={indicatorActiveColor}
        onChange={handleSwiperChange}
      >
        {banners.map((item, index) => (
          <SwiperItem key={item.id || index} className="banner__item">
            <View
              className={`banner__content ${item.linkUrl ? 'banner__content--clickable' : ''}`}
              onClick={() => handleItemClick(item, index)}
            >
              <Image
                className="banner__image"
                src={imageErrors.has(index) ? fallbackImage : item.imageUrl}
                mode="aspectFill"
                onError={() => handleImageError(index)}
                lazyLoad
              />
              
              {/* 可选的文字内容 */}
              {(item.title || item.description) && (
                <View className="banner__overlay">
                  {item.title && (
                    <View className="banner__title">{item.title}</View>
                  )}
                  {item.description && (
                    <View className="banner__description">{item.description}</View>
                  )}
                </View>
              )}
            </View>
          </SwiperItem>
        ))}
      </Swiper>

      {/* 自定义指示器 */}
      {showIndicators && banners.length > 1 && (
        <View className="banner__indicators">
          {banners.map((_, index) => (
            <View
              key={index}
              className={`banner__indicator ${
                index === currentIndex ? 'banner__indicator--active' : ''
              }`}
              style={{
                backgroundColor: index === currentIndex ? indicatorActiveColor : indicatorColor
              }}
            />
          ))}
        </View>
      )}
    </View>
  )
}

// 导出类型
export type {
  BannerProps,
  BannerItem,
  BannerState,
  LinkType,
  LinkConfig
} from './types'

// 导出工具函数
export {
  parseLinkUrl,
  navigateToPage,
  handleBannerClick,
  preloadImages,
  validateBanners,
  formatBannerData,
  getImageInfo
} from './utils'

export default Banner
