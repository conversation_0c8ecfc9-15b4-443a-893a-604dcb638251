/**
 * 纵向案例组件样式
 */

.case-list {
  width: 700rpx;
  background: #fff;
  padding: 28rpx 30rpx;
  box-sizing: border-box;

  &__loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200rpx;
    color: #999;
    font-size: 28rpx;
  }

  &__empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 80rpx 40rpx;
    text-align: center;
  }

  &__empty-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
    opacity: 0.6;
  }

  &__empty-title {
    font-size: 32rpx;
    color: #666;
    font-weight: 500;
    margin-bottom: 16rpx;
  }

  &__empty-desc {
    font-size: 24rpx;
    color: #999;
    line-height: 1.5;
    max-width: 500rpx;
  }

  &__container {
    display: flex;
    flex-direction: column;
  }
}

// 案例项样式
.case-item {
  display: flex;
  flex-direction: column;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #E0DEE5;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:active {
    background-color: #f8f8f8;
  }

  &:last-child {
    border-bottom: none;
  }

  &__title {
    font-weight: 600;
    font-size: 28rpx;
    color: #282828;
    line-height: 36rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    margin-bottom: 16rpx;
  }

  &__meta {
    display: flex;
    align-items: center;
  }

  &__category {
    font-weight: 600;
    font-size: 20rpx;
    color: #BD8A4F;
    line-height: 25rpx;
    margin-right: 20rpx;
  }

  &__clock-icon {
    width: 25rpx;
    height: 25rpx;
    margin-right: 10rpx;
  }

  &__time {
    font-weight: 600;
    font-size: 20rpx;
    color: #000000;
    line-height: 25rpx;
  }
}
