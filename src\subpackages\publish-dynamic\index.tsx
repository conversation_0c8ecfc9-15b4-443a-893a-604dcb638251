/**
 * 动态发布/编辑页面
 */
import React, { useState, useEffect } from 'react'
import { <PERSON>, Textarea, Picker, But<PERSON> } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import PageLayout from '@/components/PageLayout'
import { dynamicsApi } from '@/utils/request/apis/dynamics'
import './index.scss'

const PublishDynamic: React.FC = () => {
  const router = useRouter()
  const { id } = router.params // 动态ID，编辑时传入
  
  // 页面状态
  const [isEdit, setIsEdit] = useState(false) // 是否为编辑模式
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  
  // 表单数据
  const [formData, setFormData] = useState({
    title: '', // 动态内容
    categoryId: 0 // 分类ID
  })
  
  // 分类数据
  const [categories, setCategories] = useState<DynamicsAPI.DynamicsCategoryInfo[]>([])
  const [selectedCategoryIndex, setSelectedCategoryIndex] = useState(0)
  
  // 字数统计
  const [wordCount, setWordCount] = useState(0)
  const maxWords = 500

  // 计算按钮是否可用
  const isButtonDisabled = submitting || !formData.title.trim() || !formData.categoryId

  // 获取动态分类列表
  const loadCategories = async () => {
    try {
      const response = await dynamicsApi.getDynamicsCategoryList()
      if (response.success && response.data) {
        setCategories(response.data.list)
        if (response.data.list.length > 0 && !isEdit) {
          setFormData(prev => ({
            ...prev,
            categoryId: response.data.list[0].id
          }))
        }
      }
    } catch (error) {
      console.error('获取分类列表失败:', error)
      // 使用模拟数据作为后备
      const mockCategories = [
        { id: 1, name: '法律资讯' },
        { id: 2, name: '案例分享' },
        { id: 3, name: '专业见解' },
        { id: 4, name: '行业动态' }
      ]
      setCategories(mockCategories)
      if (!isEdit) {
        setFormData(prev => ({
          ...prev,
          categoryId: mockCategories[0].id
        }))
      }
      Taro.showToast({
        title: '使用默认分类',
        icon: 'none'
      })
    }
  }

  // 获取动态详情（编辑模式）
  const loadDynamicDetail = async (dynamicId: number) => {
    try {
      setLoading(true)
      const response = await dynamicsApi.getDynamicsDetail(dynamicId)
      if (response.success && response.data) {
        const detail = response.data.detail
        setFormData({
          title: detail.title,
          categoryId: detail.categoryId
        })
        setWordCount(detail.title.length)
        
        // 设置分类选择器的索引
        const categoryIndex = categories.findIndex(cat => cat.id === detail.categoryId)
        if (categoryIndex !== -1) {
          setSelectedCategoryIndex(categoryIndex)
        }
      }
    } catch (error) {
      console.error('获取动态详情失败:', error)
      Taro.showToast({
        title: '获取动态失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  // 处理内容输入
  const handleContentChange = (e: any) => {
    const content = e.detail.value
    if (content.length <= maxWords) {
      setFormData(prev => ({
        ...prev,
        title: content
      }))
      setWordCount(content.length)
    }
  }

  // 处理分类选择
  const handleCategoryChange = (e: any) => {
    const index = parseInt(e.detail.value)
    setSelectedCategoryIndex(index)
    if (categories[index]) {
      setFormData(prev => ({
        ...prev,
        categoryId: categories[index].id
      }))
    }
  }

  // 表单验证
  const validateForm = (): boolean => {
    if (!formData.title.trim()) {
      Taro.showToast({
        title: '请输入动态内容',
        icon: 'none'
      })
      return false
    }

    if (!formData.categoryId) {
      Taro.showToast({
        title: '请选择动态分类',
        icon: 'none'
      })
      return false
    }

    return true
  }

  // 提交发布/更新
  const handleSubmit = async () => {
    // 表单验证
    if (!validateForm()) {
      return
    }

    try {
      setSubmitting(true)

      if (isEdit && id) {
        // 更新动态
        const response = await dynamicsApi.updateDynamics({
          id: parseInt(id),
          title: formData.title.trim(),
          categoryId: formData.categoryId
        })

        if (response.success) {
          Taro.showToast({
            title: '更新成功',
            icon: 'success'
          })
        } else {
          throw new Error(response.message || '更新失败')
        }
      } else {
        // 创建动态
        const response = await dynamicsApi.createDynamics({
          title: formData.title.trim(),
          categoryId: formData.categoryId
        })

        if (response.success) {
          Taro.showToast({
            title: '发布成功',
            icon: 'success'
          })
        } else {
          throw new Error(response.message || '发布失败')
        }
      }

      // 返回上一页
      setTimeout(() => {
        Taro.navigateBack()
      }, 1500)

    } catch (error) {
      console.error('提交失败:', error)
      Taro.showToast({
        title: isEdit ? '更新失败' : '发布失败',
        icon: 'none'
      })
    } finally {
      setSubmitting(false)
    }
  }

  // 页面初始化
  useEffect(() => {
    if (id) {
      setIsEdit(true)
    }
    loadCategories()
  }, [])

  // 编辑模式下加载动态详情
  useEffect(() => {
    if (isEdit && id && categories.length > 0) {
      loadDynamicDetail(parseInt(id))
    }
  }, [isEdit, id, categories])

  return (
    <PageLayout
      title={isEdit ? '编辑动态' : '发布动态'}
      showBack={true}
      backgroundColor="#f8f9fa"
    >
      <View className="publish-dynamic">
        {loading ? (
          <View className="loading-container">
            <View className="loading-text">加载中...</View>
          </View>
        ) : (
          <>
            <View className="content-area">
              {/* 分类选择 */}
              <View className="form-section">
                <View className="form-label">动态分类</View>
                <Picker
                  mode="selector"
                  range={categories.map(cat => cat.name)}
                  value={selectedCategoryIndex}
                  onChange={handleCategoryChange}
                >
                  <View className="category-picker">
                    <View className="picker-text">
                      {categories[selectedCategoryIndex]?.name || '请选择分类'}
                    </View>
                    <View className="picker-arrow">›</View>
                  </View>
                </Picker>
              </View>

              {/* 内容输入 */}
              <View className="form-section content-section">
                <View className="form-label">
                  动态内容
                  <View className="word-count">
                    {wordCount}/{maxWords}
                  </View>
                </View>
                <Textarea
                  className="content-textarea"
                  placeholder="分享你的专业见解或日常感悟..."
                  value={formData.title}
                  onInput={handleContentChange}
                  maxlength={maxWords}
                  showConfirmBar={false}
                  adjustPosition={false}
                  autoHeight={false}
                />
              </View>
            </View>

            {/* 底部提交按钮 */}
            <View className="submit-section">
              <Button
                className="submit-btn"
                onClick={handleSubmit}
                disabled={isButtonDisabled}
                loading={submitting}
                type="primary"
                size="default"
                formType="submit"
                hoverClass={isButtonDisabled ? 'none' : 'button-hover'}
                hoverStayTime={100}
              >
                {submitting ? '提交中...' : (isEdit ? '更新动态' : '发布动态')}
              </Button>
            </View>
          </>
        )}
      </View>
    </PageLayout>
  )
}

export default PublishDynamic
