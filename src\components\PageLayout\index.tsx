/**
 * 页面布局组件
 * 
 * 提供统一的页面布局，包含导航栏、内容区域等
 */
import React, { ReactNode, useState } from 'react'
import { View, ScrollView } from '@tarojs/components'
import NavBar from '../NavBar'
import { useNavBarHeight } from '../NavBar/hooks/useNavBarHeight'
import './index.scss'

interface PageLayoutProps {
  // 页面标题
  title?: string
  // 是否显示导航栏
  showNavBar?: boolean
  // 是否显示返回按钮
  showBack?: boolean
  // 导航栏背景色
  navBarBgColor?: string
  // 导航栏文字颜色
  navBarTextColor?: string
  // 是否显示导航栏底部边框
  showNavBorder?: boolean
  // 页面背景色
  backgroundColor?: string
  // 内容区域样式类名
  contentClassName?: string
  // 是否启用下拉刷新
  enablePullRefresh?: boolean
  // 下拉刷新回调
  onPullRefresh?: () => void | Promise<void>
  // 是否启用触底加载
  enableReachBottom?: boolean
  // 触底加载回调
  onReachBottom?: () => void
  // 页面内容
  children: ReactNode
  // 自定义导航栏左侧内容
  navBarLeft?: ReactNode
  // 自定义导航栏右侧内容
  navBarRight?: ReactNode
  // 是否全屏显示（隐藏导航栏）
  fullScreen?: boolean
  // 是否安全区域适配
  safeArea?: boolean
  // 是否显示logo
  showLogo?: boolean
  // logo图片路径
  logoSrc?: string
}

const PageLayout: React.FC<PageLayoutProps> = ({
  title,
  showNavBar = true,
  showBack = true,
  navBarBgColor = '#ffffff',
  navBarTextColor = '#BD8A4F',
  showNavBorder = true,
  backgroundColor = '#f5f5f5',
  contentClassName = '',
  enablePullRefresh = false,
  onPullRefresh,
  enableReachBottom = false,
  onReachBottom,
  children,
  navBarLeft,
  navBarRight,
  fullScreen = false,
  safeArea = true,
  showLogo = false,
  logoSrc
}) => {
  // 获取导航栏高度
  const { navBarHeight } = useNavBarHeight()

  // 下拉刷新状态
  const [refreshing, setRefreshing] = useState(false)
   
  // 处理下拉刷新
  const handlePullRefresh = async () => {
    if (enablePullRefresh && onPullRefresh && !refreshing) {
      setRefreshing(true)
      try {
        await onPullRefresh()
      } finally {
        setRefreshing(false)
      }
    }
  }

  // 处理触底加载
  const handleReachBottom = () => {
    if (enableReachBottom && onReachBottom) {
      onReachBottom()
    }
  }

  return (
    <View 
      className={`page-layout ${fullScreen ? 'page-layout--fullscreen' : ''} ${safeArea ? 'page-layout--safe-area' : ''}`}
      style={{ backgroundColor }}
    >
      {/* 导航栏 */}
      {showNavBar && !fullScreen && (
        <NavBar
          title={title}
          showBack={showBack}
          backgroundColor={navBarBgColor}
          textColor={navBarTextColor}
          showBorder={showNavBorder}
          leftSlot={navBarLeft}
          rightSlot={navBarRight}
          showLogo={showLogo}
          logoSrc={logoSrc}
        />
      )}

      {/* 内容区域 */}
      <ScrollView
        className={`page-layout__content ${contentClassName}`}
        style={{
          paddingTop: showNavBar && !fullScreen ? `${navBarHeight}px` : '0px'
        }}
        scrollY
        enhanced
        showScrollbar={false}
        bounces={false}
        onScrollToLower={handleReachBottom}
        refresherEnabled={enablePullRefresh}
        refresherTriggered={refreshing}
        onRefresherRefresh={handlePullRefresh}
      >
        {children}
      </ScrollView>
    </View>
  )
}

// 导出页面状态组件
export {
  PageLoading,
  PageEmpty,
  PageError,
  PageLoadMore,
  PageContent,
  PageCard,
  PageList
} from './components/PageStates'

// 导出类型
export type {
  PageLayoutProps,
  PageState,
  PageLoadingProps,
  PageEmptyProps,
  PageErrorProps,
  PageLoadMoreProps,
  PageContentProps,
  PageCardProps,
  PageListProps,
  PaginationData,
  PageConfig
} from './types'

export default PageLayout
