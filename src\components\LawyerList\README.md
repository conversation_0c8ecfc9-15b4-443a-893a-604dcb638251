# LawyerList 律师列表组件

展示最多3个律师卡片的列表组件，支持外部传入筛选条件，具有精美的卡片设计和完整的交互功能。

## 功能特性

- ✅ **最多3个律师**：自动限制显示数量，适合首页推荐等场景
- ✅ **外部筛选条件**：支持传入API筛选参数，灵活控制数据
- ✅ **精美卡片设计**：690rpx×320rpx卡片，带背景图片
- ✅ **完整律师信息**：身份证照片、姓名、等级、文章案例数、地区、电话
- ✅ **自动跳转**：点击律师卡片自动跳转到律师详情页
- ✅ **加载状态**：支持加载中和空状态显示
- ✅ **TypeScript**：完整的类型定义支持

## 卡片设计规格

- **卡片尺寸**：690rpx × 320rpx
- **卡片间距**：20rpx
- **卡片内边距**：20rpx
- **背景图片**：`src/assets/images/layer_card_bg.png`
- **头像尺寸**：200rpx × 280rpx
- **头像右边距**：30rpx

## 基础用法

```tsx
import React from 'react'
import { View } from '@tarojs/components'
import LawyerList from '@/components/LawyerList'

const MyComponent = () => {
  return (
    <View>
      <LawyerList />
    </View>
  )
}
```

## 带筛选条件使用

```tsx
import React, { useState } from 'react'
import LawyerList from '@/components/LawyerList'

const FilterExample = () => {
  const [filterParams, setFilterParams] = useState({
    city: '深圳'
  })

  return (
    <LawyerList filterParams={filterParams} />
  )
}
```

## API 参数

### LawyerListProps

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| filterParams | LawyerAPI.GetLawyerListRequest | {} | 律师列表接口筛选条件 |
| className | string | '' | 自定义样式类名 |

**注意：** 组件内部自动处理律师卡片点击，会跳转到律师详情页 `/pages/lawyer/detail?userId=${userId}`

### LawyerCardInfo

| 字段 | 类型 | 说明 |
|------|------|------|
| userId | number | 律师用户ID |
| name | string | 律师姓名 |
| phone | string | 联系电话 |
| province | string | 省份 |
| city | string | 城市 |
| district | string | 区县 |
| idCard | string | 身份证号 |
| idCardFrontUrl | string | 身份证正面照片URL |
| idCardBackUrl | string | 身份证背面照片URL |
| isGoldenRescue | number | 是否加入黄金救援：1-是，2-否 |
| licenseUrl | string | 执业证书URL |
| authStatus | number | 认证状态：0-删除，1-待审核，2-通过，3-未通过 |
| rejectReason | string | 拒绝原因 |
| articleNum | number | 文章数量 |
| caseNum | number | 案例数量 |
| lawyerLevel | number | 律师等级：1积分律师，2协办律师，3品牌律师，4金牌律师，5金牌大律师 |
| reviewer | string | 审核人 |
| reviewTime | string | 审核时间 |
| createdAt | string | 创建时间 |

### LawyerLevel

律师等级数字对应关系：

| 数字等级 | 等级名称 | 图标文件 | 说明 |
|---------|---------|----------|------|
| 1 | 积分律师 | level1.png | 基础等级律师 |
| 2 | 协办律师 | level2.png | 协助办案律师 |
| 3 | 品牌律师 | level3.png | 品牌认证律师 |
| 4 | 金牌律师 | level4.png | 金牌认证律师 |
| 5 | 金牌大律师 | level5.png | 最高等级律师 |

等级图标路径：`src/assets/images/layer-level-icon/level[1-5].png`

## 卡片布局说明

```
┌─────────────────────────────────────────────────────────┐
│  ┌─────────┐  ┌─────────────────────────────────────┐   │
│  │         │  │ 👤 张律师 ⭐                        │   │
│  │ 身份证  │  │                                     │   │
│  │ 正面照  │  │ 已发表5篇文章，处理20个案例          │   │
│  │ 200×280 │  │                                     │   │
│  │         │  │                                     │   │
│  │         │  │ 执业地区：广东省深圳市南山区          │   │
│  └─────────┘  │ 联系电话：13800138000               │   │
│               └─────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

## 样式定制

组件提供了完整的样式类名，可以通过 CSS 进行定制：

```scss
.lawyer-list {
  // 自定义列表容器样式
}

.lawyer-card {
  // 自定义卡片样式
  
  &__avatar {
    // 自定义头像样式
  }
  
  &__name {
    // 自定义姓名样式
  }
  
  &__description {
    // 自定义简介样式
  }
}
```

## 注意事项

1. **图片资源**：确保以下图片文件存在
   - `src/assets/images/layer_card_bg.png`（卡片背景图）
   - `src/assets/images/layer-level-icon/level1.png`（一级律师图标）
   - `src/assets/images/layer-level-icon/level2.png`（二级律师图标）
   - `src/assets/images/layer-level-icon/level3.png`（三级律师图标）
   - `src/assets/images/layer-level-icon/level4.png`（四级律师图标）
   - `src/assets/images/layer-level-icon/level5.png`（五级律师图标）
2. **API依赖**：组件依赖 `lawyerApi.getLawyerList` 接口
3. **等级字段**：接口需要返回 `level` 字段，类型为 `'level1' | 'level2' | 'level3' | 'level4' | 'level5'`
4. **数量限制**：组件自动限制最多显示3个律师
5. **响应式**：组件支持移动端响应式适配
6. **错误处理**：内置了加载失败的错误处理逻辑

## 接口依赖

组件依赖以下API接口：

- **接口**：`lawyerApi.getLawyerList(params)`
- **参数**：`LawyerAPI.GetLawyerListRequest`
- **返回**：`LawyerAPI.PaginationResponse<LawyerAPI.LawyerInfo>`
