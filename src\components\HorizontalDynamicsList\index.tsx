/**
 * 横向动态列表组件
 * 
 * 展示最多5个动态卡片，支持横向滑动，支持外部传入筛选条件
 */
import React, { useState, useEffect } from 'react'
import { View, ScrollView, Text, Image } from '@tarojs/components'
import { dynamicsApi } from '@/utils/request/apis'
import { navigateToPage } from '@/utils'
import ClockIcon from '@/assets/images/common-icon/clock.png'
import EyeIcon from '@/assets/images/common-icon/eye.png'
import { HorizontalDynamicsListProps } from './types'
import './index.scss'

const HorizontalDynamicsList: React.FC<HorizontalDynamicsListProps> = ({
  filterParams,
  className = ''
}) => {
  // 动态列表数据
  const [dynamics, setDynamics] = useState<DynamicsAPI.DynamicsListInfo[]>([])
  // 加载状态
  const [isLoading, setIsLoading] = useState(false)

  // 格式化时间
  const formatTime = (timeStr: string): string => {
    const date = new Date(timeStr)
    return `${date.getMonth() + 1}-${String(date.getDate()).padStart(2, '0')}`
  }

  // 加载动态列表数据
  const loadDynamics = async () => {
    try {
      setIsLoading(true)
      const params = {
        ...filterParams,
        page: 1,
        pageSize: 5 // 最多展示5个动态
      }

      const response = await dynamicsApi.getDynamicsList(params)
      if (response.success && response.data) {
        setDynamics(response.data.list)
      }
    } catch (error) {
      // 模拟数据作为fallback
      const mockList: DynamicsAPI.DynamicsListInfo[] = [
        {
          id: 1,
          title: "最新法律法规解读：民法典实施后的重要变化及其对日常生活的影响分析",
          categoryId: 1,
          categoryName: "法律资讯",
          viewCount: 1250,
          createdAt: "2024-01-15T10:30:00Z"
        },
        {
          id: 2,
          title: "成功案例分享：某企业合同纠纷案件处理经验与实务要点总结",
          categoryId: 2,
          categoryName: "案例分享",
          viewCount: 890,
          createdAt: "2024-01-12T14:20:00Z"
        },
        {
          id: 3,
          title: "专业见解：如何有效防范企业法律风险，建立完善的合规体系",
          categoryId: 3,
          categoryName: "专业见解",
          viewCount: 2100,
          createdAt: "2024-01-10T09:15:00Z"
        },
        {
          id: 4,
          title: "行业动态：律师行业数字化转型趋势与发展前景分析报告",
          categoryId: 4,
          categoryName: "行业动态",
          viewCount: 1680,
          createdAt: "2024-01-08T16:45:00Z"
        },
        {
          id: 5,
          title: "法规解读：新修订的公司法对中小企业经营管理的影响解析",
          categoryId: 5,
          categoryName: "法规解读",
          viewCount: 950,
          createdAt: "2024-01-05T11:30:00Z"
        }
      ]
      setDynamics(mockList)
      console.error('加载动态列表失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 处理动态卡片点击 - 跳转到动态详情页
  const handleDynamicsClick = (dynamicsInfo: DynamicsAPI.DynamicsListInfo) => {
    navigateToPage(`/subpackages/detail/dynamics/index?id=${dynamicsInfo.id}`)
  }

  // 组件挂载时加载数据
  useEffect(() => {
    loadDynamics()
  }, [filterParams])

  // 显示加载状态
  const showLoading = isLoading

  return (
    <View className={`horizontal-dynamics-list ${className}`}>
      {showLoading ? (
        <View className='horizontal-dynamics-list__loading'>
          <Text>加载中...</Text>
        </View>
      ) : (
        <>
          {dynamics.length > 0 ? (
            <ScrollView
              className='horizontal-dynamics-list__scroll-view'
              scrollX
              showScrollbar={false}
              enhanced
              bounces={false}
            >
              <View className='horizontal-dynamics-list__container'>
                {dynamics.map((dynamicsInfo) => (
                  <View
                    key={dynamicsInfo.id}
                    className='horizontal-dynamics-card'
                    onClick={() => handleDynamicsClick(dynamicsInfo)}
                  >
                    <Text className='horizontal-dynamics-card__title'>{dynamicsInfo.title}</Text>
                    <View className='horizontal-dynamics-card__meta'>
                      <Text className='horizontal-dynamics-card__category'>{dynamicsInfo.categoryName}</Text>
                      <Image src={ClockIcon} mode='aspectFit' className='horizontal-dynamics-card__info-icon' />
                      <Text className='horizontal-dynamics-card__info-text'>{formatTime(dynamicsInfo.createdAt)}</Text>
                      <Image src={EyeIcon} mode='aspectFit' className='horizontal-dynamics-card__info-icon' />
                      <Text className='horizontal-dynamics-card__info-text'>{dynamicsInfo.viewCount}</Text>
                    </View>
                  </View>
                ))}
              </View>
            </ScrollView>
          ) : (
            <View className='horizontal-dynamics-list__empty'>
              <Text className='horizontal-dynamics-list__empty-title'>暂无动态信息</Text>
              <Text className='horizontal-dynamics-list__empty-desc'>当前没有相关动态，请稍后再试</Text>
            </View>
          )}
        </>
      )}
    </View>
  )
}

// 导出组件
export default HorizontalDynamicsList

export type { HorizontalDynamicsListProps } from './types'
