{"libVersion": "3.8.9", "setting": {"urlCheck": false}, "condition": {"miniprogram": {"list": [{"name": "subpackages/detail/lawyer/index", "pathName": "subpackages/detail/lawyer/index", "query": "userId=4", "scene": null, "launchMode": "default"}, {"name": "subpackages/publish-case/index", "pathName": "subpackages/publish-case/index", "query": "", "launchMode": "default", "scene": null}, {"name": "subpackages/publish-dynamic/index", "pathName": "subpackages/publish-dynamic/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/mine/index", "pathName": "pages/mine/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/case/index", "pathName": "pages/case/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/lawyer/index", "pathName": "pages/lawyer/index", "query": "", "launchMode": "default", "scene": null}]}}}