/**
 * 请求库全局类型声明文件
 *
 * 此文件统一导入所有类型声明，避免重复引用
 */

// 导入通用类型（必须最先导入）
/// <reference path="./common-types.d.ts" />

// 导入核心请求类型
/// <reference path="./types.ts" />

// 导入各API模块的类型声明
/// <reference path="./apis/user/typing.d.ts" />
/// <reference path="./apis/lawyer/typing.d.ts" />
/// <reference path="./apis/case/typing.d.ts" />
/// <reference path="./apis/article/typing.d.ts" />
/// <reference path="./apis/upload/typing.d.ts" />
/// <reference path="./apis/common/typing.d.ts" />

// 导出空对象以使此文件成为模块
export {}
