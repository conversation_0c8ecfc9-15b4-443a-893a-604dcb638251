/**
 * 文章相关接口
 * 基于 api.json 接口文档定义
 */
import request from '../../index'

// 文章相关接口
export const articleApi = {
  // 获取文章列表
  getArticleList: (params?: ArticleAPI.GetArticleListRequest) =>
    request.get<ArticleAPI.GetArticleListResponse>('/mini/find-law-article/list', params),

  // 获取文章详情
  getArticleDetail: (articleId: number) =>
    request.get<ArticleAPI.GetArticleDetailResponse>(`/mini/find-law-article/${articleId}/detail`),

  // 获取律师文章列表
  getLawyerArticleList: (lawyerId: number, params?: ArticleAPI.GetLawyerArticleListRequest) =>
    request.get<ArticleAPI.GetLawyerArticleListResponse>(`/mini/find-law-article/${lawyerId}/list`, params),
}
