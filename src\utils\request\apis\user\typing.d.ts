/**
 * 用户模块类型声明
 */

// 引用通用类型
/// <reference path="../../common-types.d.ts" />

declare namespace UserAPI {
  // 用户信息类型
  interface UserInfo extends CommonTypes.BaseEntity {
    username: string
    email?: string
    avatar?: string
    nickname?: string
    phone?: string
  }

  // 登录请求参数
  interface LoginRequest {
    username: string
    password: string
  }

  // 注册请求参数
  interface RegisterRequest extends LoginRequest {
    email?: string
  }

  // 登录响应数据
  interface LoginResponse {
    token: string
    userInfo: UserInfo
  }

  // 更新用户信息请求参数
  interface UpdateUserInfoRequest extends Partial<Omit<UserInfo, 'id' | 'createTime' | 'updateTime'>> {}

  // 刷新token响应
  interface RefreshTokenResponse {
    token: string
  }
}
