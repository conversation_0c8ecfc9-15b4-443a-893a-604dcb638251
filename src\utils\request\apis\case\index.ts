/**
 * 案例相关接口
 * 基于 api.json 接口文档定义
 */
import request from '../../index'

// 案例相关接口
export const caseApi = {
  // 获取案例列表
  getCaseList: (params?: CaseAPI.GetCaseListRequest) =>
    request.get<CaseAPI.GetCaseListResponse>('/mini/find-law-case/list', params),

  // 获取案例详情
  getCaseDetail: (caseId: number) =>
    request.get<CaseAPI.GetCaseDetailResponse>(`/mini/find-law-case/${caseId}/detail`),

  // 获取律师案例列表
  getLawyerCaseList: (lawyerId: number, params?: CaseAPI.GetLawyerCaseListRequest) =>
    request.get<CaseAPI.GetLawyerCaseListResponse>(`/mini/find-law-case/${lawyerId}/list`, params),

  // 获取案例分类列表
  getCaseCategoryList: () =>
    request.get<CaseAPI.GetCaseCategoryListResponse>('/mini/law-case/category-list'),

  // 创建案例
  createCase: (params: CaseAPI.CreateCaseRequest) =>
    request.post<CaseAPI.CreateCaseResponse>('/mini/law-case/create', params),

  // 更新案例
  updateCase: (params: CaseAPI.UpdateCaseRequest) =>
    request.put<CaseAPI.UpdateCaseResponse>(`/mini/law-case/${params.id}/update`, params),
}
