/**
 * 案例相关接口
 * 基于 api.json 接口文档定义
 */
import request from '../../index'
import { BaseResponse } from '../../types'

// 案例相关接口
export const caseApi = {
  // 获取案例列表（基于 /mini/find-law-case/list）
  getCaseList: (params?: CaseAPI.GetCaseListRequest): Promise<BaseResponse<CaseAPI.GetCaseListResponse>> =>
    request.get<CaseAPI.GetCaseListResponse>('/mini/find-law-case/list', params),

  // 获取案例详情（基于 /mini/find-law-case/:caseId/detail）
  getCaseDetail: (caseId: number): Promise<BaseResponse<CaseAPI.GetCaseDetailResponse>> =>
    request.get<CaseAPI.GetCaseDetailResponse>(`/mini/find-law-case/${caseId}/detail`),

  // 获取推荐案例列表（基于 /mini/find-law-case/:caseId/recommend-list）
  getRecommendCaseList: (caseId: number, params?: CaseAPI.GetRecommendCaseListRequest): Promise<BaseResponse<CaseAPI.GetRecommendCaseListResponse>> =>
    request.get<CaseAPI.GetRecommendCaseListResponse>(`/mini/find-law-case/${caseId}/recommend-list`, params),

  // 获取律师案例列表（基于 /mini/find-law-case/:lawyerId/list）
  getLawyerCaseList: (lawyerId: number, params?: CaseAPI.GetLawyerCaseListRequest): Promise<BaseResponse<CaseAPI.GetLawyerCaseListResponse>> =>
    request.get<CaseAPI.GetLawyerCaseListResponse>(`/mini/find-law-case/${lawyerId}/list`, params),

  // 获取案例分类列表（基于 /mini/common/law-case/category-list）
  getCaseCategoryList: (): Promise<BaseResponse<CaseAPI.GetCaseCategoryListResponse>> =>
    request.get<CaseAPI.GetCaseCategoryListResponse>('/mini/common/law-case/category-list'),

  // 获取案件阶段列表（基于 /mini/common/law-order/case-stage-list）
  getCaseStageList: (): Promise<BaseResponse<CaseAPI.GetCaseStageListResponse>> =>
    request.get<CaseAPI.GetCaseStageListResponse>('/mini/common/law-order/case-stage-list'),

  // 获取律师个人中心案例列表（基于 /mini/owner-center-lawyer/law-case/list）
  getOwnerCenterCaseList: (params?: CaseAPI.GetOwnerCenterCaseListRequest): Promise<BaseResponse<CaseAPI.GetOwnerCenterCaseListResponse>> =>
    request.get<CaseAPI.GetOwnerCenterCaseListResponse>('/mini/owner-center-lawyer/law-case/list', params),

  // 创建案例
  createCase: (params: CaseAPI.CreateCaseRequest): Promise<BaseResponse<CaseAPI.CreateCaseResponse>> =>
    request.post<CaseAPI.CreateCaseResponse>('/mini/law-case/create', params),

  // 更新案例
  updateCase: (params: CaseAPI.UpdateCaseRequest): Promise<BaseResponse<CaseAPI.UpdateCaseResponse>> =>
    request.put<CaseAPI.UpdateCaseResponse>(`/mini/law-case/${params.id}/update`, params),
}
