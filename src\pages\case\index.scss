/**
 * 案例页面样式
 */

.case-content {
  display: flex;
  flex-direction: column;
  gap: 30rpx;

  &__header {
    display: flex;
    flex-direction: column;
    gap: 30rpx;
    padding: 30rpx;
    background: #fff;
    border-radius: 20rpx;
  }

  &__list {
    padding:0 25rpx;
    border-radius: 50rpx;
    overflow: hidden;
  }
}

// 搜索容器
.search-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

// 搜索框
.search-box {
  position: relative;
  display: flex;
  align-items: center;
  width: 700rpx;
  height: 88rpx;
  background: #F7F7F7;
  border-radius: 44rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;

  &::placeholder {
    color: #999;
  }
}

// 分类容器
.category-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

// 分类项
.category-item {
  padding: 15rpx 20rpx;
  background: #FFFFFF;
  border-radius: 40rpx;
  border: 1rpx solid #E6E9ED;
  display: flex;
  align-items: center;
  justify-content: center;


  &--active {
    border: 1rpx solid #BD8A4F;

    .category-text {
      color: #BD8A4F;
    }
  }
}

.category-text {
  font-size: 24rpx;
  color: #333;
  line-height: 1;
}