/**
 * 律师擅长领域组件样式
 */

.lawyer-fields {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

  &__title {
    font-size: 32rpx;
    font-weight: 600;
    color: #000000;
    display: block;
  }

  &__list {
    margin-top: 30rpx;
    display: flex;
    flex-wrap: wrap;
    gap: 15rpx;
  }

  &__tag {
    width: 160rpx;
    height: 56rpx;
    background: #BD8A4F;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &--clickable {
      cursor: pointer;

      &:hover {
        background: #A67A3F;
        transform: translateY(-2rpx);
      }

      &:active {
        transform: translateY(0);
      }
    }

    &--more {
      background: #F0F0F0;
      color: #999;
    }
  }

  &__text {
    font-weight: 600;
    font-size: 28rpx;
    color: #FFFFFF;
    display: block;
  }
}
