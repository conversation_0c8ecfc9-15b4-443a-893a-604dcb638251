/**
 * Banner 轮播组件类型定义
 */

// 单个 Banner 项目接口
export interface BannerItem {
  // 图片 URL
  imageUrl: string
  // 点击跳转 URL (可选)
  linkUrl?: string
  // Banner 标题 (可选)
  title?: string
  // Banner 描述 (可选)
  description?: string
  // 唯一标识 (可选，用于 key)
  id?: string | number
}

// Banner 组件属性接口
export interface BannerProps {
  // Banner 数据列表
  banners: BannerItem[]
  // 是否自动播放
  autoplay?: boolean
  // 自动播放间隔时间 (毫秒)
  interval?: number
  // 是否显示指示器
  showIndicators?: boolean
  // 是否显示导航点
  showDots?: boolean
  // 指示器颜色
  indicatorColor?: string
  // 当前指示器颜色
  indicatorActiveColor?: string
  // 是否循环播放
  circular?: boolean
  // 切换动画时长 (毫秒)
  duration?: number
  // 图片加载失败时的占位图
  fallbackImage?: string
  // 自定义样式类名
  className?: string
  // 点击事件回调
  onClick?: (item: BannerItem, index: number) => void
  // 切换事件回调
  onChange?: (current: number) => void
}

// Banner 组件状态接口
export interface BannerState {
  // 当前显示的索引
  currentIndex: number
  // 是否正在加载
  loading: boolean
}

// 跳转类型枚举
export enum LinkType {
  // 内部页面跳转
  NAVIGATE = 'navigate',
  // 外部链接
  EXTERNAL = 'external',
  // 小程序内跳转
  SWITCH_TAB = 'switchTab',
  // 重定向
  REDIRECT = 'redirect'
}

// 跳转配置接口
export interface LinkConfig {
  // 跳转类型
  type: LinkType
  // 跳转地址
  url: string
  // 额外参数
  params?: Record<string, any>
}
