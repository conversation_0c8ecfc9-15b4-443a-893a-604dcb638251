/**
 * 律师页面样式
 */
.area-select-trigger{
  display: flex;
  align-items: center;
  gap: 10rpx;
  &__text{
    font-size: 28rpx;
    color: #333;
    &--placeholder{
      color: #999;
    }
  }
  &__arrow{
    width: 44rpx;
    height: 44rpx;
  }
}
.lawyer-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;

  .lawyer-list-container {
    padding: 0 30rpx;
  }
}

// 搜索栏样式
.search-bar {
  height: 88rpx;
  width: 750rpx;
  background: #fff;
  padding: 22rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;

  &__left,
  &__right {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  &__icon {
    width: 44rpx;
    height: 44rpx;
  }

  &__text {
    font-weight: 600;
    font-size: 28rpx;
    color: #727E8C;
    line-height: 32rpx;
  }

  &__left {
    display: flex;
    align-items: center;
  }

  &__right {
    .search-bar__text {
      margin-right: 8rpx;
    }
  }

  &__input {
    flex: 1;
    height: 66rpx;
    margin: 0 20rpx;
    padding: 0 15rpx;
    background: #fff;
    border-radius: 50rpx;
    font-size: 28rpx;
    color: #333;
    box-sizing: border-box;
    border: 2rpx solid #f0f0f0;
  }

  &__placeholder {
    color: #999;
    font-size: 28rpx;
  }
}

// 筛选弹窗样式 - 使用PageContainer
.filter-container {
  background: #fff;
  min-height: 60vh;
  max-height: 80vh;
  display: flex;
  flex-direction: column;

  &__header {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx;
    border-bottom: 2rpx solid #f0f0f0;
    flex-shrink: 0;
  }

  &__title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  &__close {
    position: absolute;
    right: 30rpx;
    font-size: 40rpx;
    color: #999;
    cursor: pointer;
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    &:active {
      color: #666;
    }
  }

  &__body {
    flex: 1;
    padding: 30rpx;
    overflow-y: auto;
    min-height: 300rpx;
  }

  &__footer {
    flex-shrink: 0;
    padding: 20rpx 30rpx 30rpx;
    border-top: 2rpx solid #f0f0f0;
  }

  &__buttons {
    display: flex;
    gap: 20rpx;
  }

  &__reset,
  &__confirm {
    flex: 1;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  &__reset {
    background: #f5f5f5;
    color: #666;

    &:active {
      background: #e8e8e8;
    }
  }

  &__confirm {
    background: #BD8A4F;
    color: #fff;

    &:active {
      background: #a67a42;
    }
  }
}

// 筛选区域样式
.filter-section {
  margin-bottom: 50rpx;

  &:last-child {
    margin-bottom: 0;
  }

  &__title {
    font-size: 32rpx;
    color: #282828;
    margin-bottom: 30rpx;
    display: block;
  }

  &__options {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
  }
}

// 筛选选项样式
.filter-option {
  width: 196rpx;
  height: 80rpx;
  background: #FFFFFF;
  border-radius: 40rpx;
  border: 1rpx solid #E6E9ED;
  font-weight: 400;
  font-size: 30rpx;
  color: #000000;
  line-height: 42rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;

  // 文字超过4个字省略显示
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 20rpx;

  // 选中态样式
  &--selected {
    border: 1rpx solid #BD8A4F;
    color: #BD8A4F;
  }
}

// 筛选加载和空状态样式
.filter-loading,
.filter-empty {
  width: 100%;
  text-align: center;
  color: #999;
  font-size: 28rpx;
  line-height: 40rpx;
  padding: 40rpx 0;
}

.filter-loading {
  color: #666;
}