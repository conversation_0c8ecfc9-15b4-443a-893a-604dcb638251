import React, { useEffect, useState } from 'react'
import { View, Text, Input, Image, PageContainer } from '@tarojs/components'
import PageLayout, { PageContent } from '@/components/PageLayout'
import LawyerList from '@/components/LawyerList'
import AreaSelect from '@/components/AreaSelect'

import Banner, { BannerItem } from '@/components/Banner'
import CaseBanner from '@/assets/images/case_bg.png'
import SearchIcon from '@/assets/images/common-icon/search.png'
import MenuIcon from '@/assets/images/common-icon/menu.png'
import ClickMoreIcon from '@/assets/images/common-icon/click_more.png'
import { getStorage, removeStorage } from '@/utils'
import './index.scss'

const Lawyer: React.FC = () => {
  // filterParams
  const [filterParams, setFilterParams] = useState<LawyerAPI.GetLawyerListRequest>({})
  // 搜索关键词状态
  const [searchKeyword, setSearchKeyword] = useState('')
  // 筛选弹窗显示状态
  const [showFilterModal, setShowFilterModal] = useState(false)
  // 筛选条件状态
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null)
  const [selectedLawyerLevel, setSelectedLawyerLevel] = useState<number | null>(null)
  // 案件类型数据状态
  const [caseCategories, setCaseCategories] = useState<Array<{ id: number, name: string }>>([])
  // 案件类型加载状态
  const [categoriesLoading, setCategoriesLoading] = useState(false)
  // 选中的地区
  const [selectedArea, setSelectedArea] = useState('')
  const [isLoadLawyer, setIsLoadLawyer] = useState(false)

  // 律师等级数据
  const lawyerLevels = [
    { id: 1, name: '积分律师' },
    { id: 2, name: '协办律师' },
    { id: 3, name: '品牌律师' },
    { id: 4, name: '金牌律师' },
    { id: 5, name: '金牌大律师' }
  ]


  // 获取案件类型数据
  const loadCaseCategories = async () => {
    try {
      setCategoriesLoading(true)
      // TODO: 替换为实际的API调用
      // const response = await caseApi.getCaseCategoryList()
      // if (response.success && response.data) {
      //   setCaseCategories(response.data.list)
      // }

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 500))

      // 抛出错误以测试catch分支
      throw new Error('API not implemented yet')

    } catch (error) {
      console.error('获取案件类型失败，使用mock数据:', error)
      // 使用mock数据
      const mockCategories = [
        { id: 1, name: '劳动纠纷' },
        { id: 2, name: '房产纠纷' },
        { id: 3, name: '交通事故' },
        { id: 4, name: '婚姻家庭' },
        { id: 5, name: '公司法务' },
        { id: 6, name: '知识产权' },
        { id: 7, name: '刑事辩护' },
        { id: 8, name: '合同纠纷' }
      ]
      setCaseCategories(mockCategories)
    } finally {
      setCategoriesLoading(false)
    }
  }
  // 如果本地缓存有律师等级，默认加载筛选条件
  const loadFilterParamsFromStorage = async () => {
    try {
      const lawyerFilter = await getStorage('lawyerFilter')
      if (lawyerFilter) {
        setSelectedLawyerLevel(lawyerFilter.level)
        setFilterParams(prev => ({
          ...prev,
          lawyerLevel: lawyerFilter.level
        }))
        // 清除本地缓存
        await removeStorage('lawyerFilter')
      }
      setIsLoadLawyer(true)
    } catch (error) {
      console.error('获取筛选条件失败:', error)
    }
  }

  // 页面生命周期
  useEffect(() => {
    loadFilterParamsFromStorage()
    // 加载案件类型数据
    loadCaseCategories()
  }, [])


  // 处理搜索输入
  const handleSearchInput = (e: any) => {
    setSearchKeyword(e.detail.value)
  }
  const handleSearchInputConfirm = (e: any) => {
    setSearchKeyword(e.detail.value)
    setFilterParams(prev => ({
      ...prev,
      lawyerName: e.detail.value
    }))
  }

  // 处理筛选按钮点击
  const handleFilterClick = () => {
    setShowFilterModal(true)
  }

  // 关闭筛选弹窗
  const handleCloseFilter = () => {
    setSelectedCategoryId(filterParams.categoryId || null)
    setSelectedLawyerLevel(filterParams.lawyerLevel || null)
    setShowFilterModal(false)
  }

  // 处理案件类型选择
  const handleCategorySelect = (categoryId: number) => {
    setSelectedCategoryId(selectedCategoryId === categoryId ? null : categoryId)
  }

  // 处理律师等级选择
  const handleLawyerLevelSelect = (level: number) => {
    setSelectedLawyerLevel(selectedLawyerLevel === level ? null : level)
  }

  // 重置筛选条件
  const handleResetFilter = () => {
    setSelectedCategoryId(null)
    setSelectedLawyerLevel(null)
  }

  // 确认筛选
  const handleConfirmFilter = () => {
    // 这里可以将筛选条件传递给LawyerList组件
    setFilterParams({
      categoryId: selectedCategoryId || undefined,
      lawyerLevel: (selectedLawyerLevel as LawyerAPI.LawyerLevel) || undefined
    })
    setShowFilterModal(false)
  }
  const handleAreaChange = (result) => {
    // 只显示最后一级
    const areas = result.areaString.split('/')
    const lastArea = areas[areas.length - 1]
    const nextAreaObj = {
      province: result.province.name,
      city: result.city.name,
      district: result.district?.name
    }
    setSelectedArea(lastArea)
    setFilterParams(prev => ({
      ...prev,
      ...nextAreaObj
    }))
  }
  const banners: BannerItem[] = [
    {
      id: 'home-banner-1',
      imageUrl: CaseBanner,
      linkUrl: '/pages/case/index'
    }
  ]
  const LeftSolt = () => {
    return (
      <AreaSelect onChange={handleAreaChange}>
        <View className='area-select-trigger'>
          <Text className={`area-select-trigger__text ${!selectedArea ? 'area-select-trigger__text--placeholder' : ''}`}>
            {selectedArea || '全国'}
          </Text>
          <Image className='area-select-trigger__arrow' src={ClickMoreIcon} mode='aspectFit'></Image>
        </View>
      </AreaSelect>
    )
  }
  console.log('filterParams',filterParams)
  return (
    <PageLayout
      title='找律师'
      showBack={false}
      backgroundColor='#f8f9fa'
      navBarLeft={LeftSolt()}
    >
      <PageContent padded='b'>
        <Banner
          banners={banners}
          autoplay={false}
          showIndicators={false}
          showDots={false}
          circular={false}
          className='lawyer-banner'
        />
        <View className='lawyer-content'>
          <View className='search-bar'>
            {/* 左侧搜索图标 */}
            <View className='search-bar__left' onClick={handleSearchInputConfirm}>
              <Image className='search-bar__icon' src={SearchIcon} mode='aspectFit' />
            </View>

            {/* 中间搜索输入框 */}
            <Input
              className='search-bar__input'
              placeholder='请输入律师姓名'
              value={searchKeyword}
              onInput={handleSearchInput}
              confirmType='search'
              confirmHold
              onConfirm={handleSearchInputConfirm}
              placeholderClass='search-bar__placeholder'
            />

            {/* 右侧筛选文字和图标 */}
            <View className='search-bar__right' onClick={handleFilterClick}>
              <Text className='search-bar__text'>筛选</Text>
              <Image className='search-bar__icon' src={MenuIcon} mode='aspectFit' />
            </View>
          </View>

          <View className='lawyer-list-container'>
           {isLoadLawyer? <LawyerList filterParams={filterParams} />:null}
          </View>
        </View>

        {/* 筛选弹窗 - 使用PageContainer */}
        <PageContainer
          show={showFilterModal}
          position='bottom'
          round
          overlay
          onClickOverlay={handleCloseFilter}
          onAfterLeave={handleCloseFilter}
        >
          <View className='filter-container'>
            {/* 弹窗头部 */}
            <View className='filter-container__header'>
              <Text className='filter-container__title'>筛选</Text>
              <Text className='filter-container__close' onClick={handleCloseFilter}>×</Text>
            </View>

            {/* 弹窗内容 */}
            <View className='filter-container__body'>
              {/* 案件类型筛选 */}
              <View className='filter-section'>
                <Text className='filter-section__title'>案件类型</Text>
                <View className='filter-section__options'>
                  {categoriesLoading ? (
                    <Text className='filter-loading'>加载中...</Text>
                  ) : caseCategories.length > 0 ? (
                    caseCategories.map((category) => (
                      <Text
                        key={category.id}
                        className={`filter-option ${selectedCategoryId === category.id ? 'filter-option--selected' : ''}`}
                        onClick={() => handleCategorySelect(category.id)}
                      >
                        {category.name}
                      </Text>
                    ))
                  ) : (
                    <Text className='filter-empty'>暂无案件类型</Text>
                  )}
                </View>
              </View>

              {/* 律师等级筛选 */}
              <View className='filter-section'>
                <Text className='filter-section__title'>律师等级</Text>
                <View className='filter-section__options'>
                  {lawyerLevels.map((level) => (
                    <Text
                      key={level.id}
                      className={`filter-option ${selectedLawyerLevel === level.id ? 'filter-option--selected' : ''}`}
                      onClick={() => handleLawyerLevelSelect(level.id)}
                    >
                      {level.name}
                    </Text>
                  ))}
                </View>
              </View>
            </View>

            {/* 弹窗底部操作按钮 */}
            <View className='filter-container__footer'>
              <View className='filter-container__buttons'>
                <Text className='filter-container__reset' onClick={handleResetFilter}>
                  重置
                </Text>
                <Text className='filter-container__confirm' onClick={handleConfirmFilter}>
                  确定
                </Text>
              </View>
            </View>
          </View>
        </PageContainer>
      </PageContent>
    </PageLayout>
  )
}

export default Lawyer
