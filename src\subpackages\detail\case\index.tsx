/**
 * 案例详情页面
 */
import React, { useState, useEffect } from 'react'
import { View, Text, Button } from '@tarojs/components'
import { useRouter } from '@tarojs/taro'
import { caseApi } from '@/utils/request/apis'
import { PageLayout } from '@/components'
import './index.scss'

interface CaseDetailInfo {
  id: number
  title: string
  categoryId: number
  categoryName: string
  content: string
  viewCount: number
  creator: string
  creatorId: number
  createdAt: string
}

const CaseDetail: React.FC = () => {
  const router = useRouter()
  const { id } = router.params
  
  const [caseInfo, setCaseInfo] = useState<CaseDetailInfo | null>(null)
  const [loading, setLoading] = useState(true)



  // 格式化时间
  const formatTime = (timeStr: string): string => {
    const date = new Date(timeStr)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  }

  // 加载案例详情
  const loadCaseDetail = async () => {
    try {
      setLoading(true)
      if (id) {
        const response = await caseApi.getCaseDetail(Number(id))
        if (response.success && response.data) {
          setCaseInfo(response.data.detail)
        }
      }
    } catch (error) {
      // 模拟数据
      const mockData: CaseDetailInfo = {
        id: Number(id) || 1,
        title: '劳动合同纠纷案例分析：员工被无故辞退如何维权',
        categoryId: 1,
        categoryName: '劳动纠纷',
        viewCount: 1250,
        creator: '张律师',
        creatorId: 1,
        createdAt: '2025-06-20 10:00:00',
        content: `
案例背景：
李某于2023年1月入职某科技公司，签订了为期三年的劳动合同。2024年5月，公司以"业务调整"为由，突然通知李某解除劳动合同，但未支付任何经济补偿。

争议焦点：
1. 公司是否有权单方面解除劳动合同？
2. 员工是否有权要求经济补偿？
3. 如何计算经济补偿标准？

法律分析：
根据《劳动合同法》相关规定，用人单位不得随意解除劳动合同。本案中，公司以"业务调整"为由解除合同，属于违法解除。

处理结果：
经过劳动仲裁，裁决公司支付李某：
1. 违法解除劳动合同赔偿金：3个月工资
2. 未提前通知解除合同的代通知金：1个月工资
3. 未休年假工资：按日工资标准计算

案例启示：
1. 劳动者应了解自己的合法权益
2. 保留相关证据材料
3. 及时寻求法律帮助
4. 通过合法途径维权
        `,

      }
      setCaseInfo(mockData)
      console.error('加载案例详情失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadCaseDetail()
  }, [id])

  return (
    <PageLayout
      title="案例详情"
      loading={loading}
      className="case-detail"
    >
      {caseInfo && (
        <View className="case-detail__content">
          {/* 案例标题 */}
          <Text className="case-title">{caseInfo.title}</Text>
          
          {/* 案例信息 */}
          <View className="case-meta">
            <View className="case-meta__row">
              <Text className="case-meta__label">分类：</Text>
              <Text className="case-meta__value">{caseInfo.categoryName}</Text>
            </View>

            <View className="case-meta__row">
              <Text className="case-meta__label">作者：</Text>
              <Text className="case-meta__value">{caseInfo.creator}</Text>
            </View>
            <View className="case-meta__row">
              <Text className="case-meta__label">发布时间：</Text>
              <Text className="case-meta__value">
                {formatTime(caseInfo.createdAt)}
              </Text>
            </View>
            <View className="case-meta__row">
              <Text className="case-meta__label">浏览量：</Text>
              <Text className="case-meta__value">{caseInfo.viewCount}</Text>
            </View>
          </View>



          {/* 案例内容 */}
          <View className="case-content">
            <Text className="case-content__title">案例详情</Text>
            <Text className="case-content__text">
              {caseInfo.content || '暂无详细内容'}
            </Text>
          </View>

          {/* 操作按钮 */}
          <View className="case-actions">
            <Button className="case-actions__btn" type="primary">
              咨询相关问题
            </Button>
            <Button className="case-actions__btn" plain>
              收藏案例
            </Button>
          </View>
        </View>
      )}
    </PageLayout>
  )
}

export default CaseDetail
