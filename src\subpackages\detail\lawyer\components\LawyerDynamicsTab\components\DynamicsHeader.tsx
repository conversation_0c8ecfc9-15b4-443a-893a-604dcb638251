/**
 * 动态标题和搜索框组件
 */
import React from 'react'
import { View, Text, Input, Image } from '@tarojs/components'
import searchIcon from '@/assets/images/common-icon/search_g.png'

// 组件属性
export interface DynamicsHeaderProps {
  // 搜索关键词
  searchKeyword: string
  // 搜索输入回调
  onSearchInput: (e: any) => void
  // 自定义样式类名
  className?: string
}

const DynamicsHeader: React.FC<DynamicsHeaderProps> = ({
  searchKeyword,
  onSearchInput,
  className = ''
}) => {
  return (
    <View className={`dynamics-header ${className}`}>
      <Text className='dynamics-header__title'>律师动态</Text>
      <View className='dynamics-header__search'>
        <Image className='dynamics-header__search-icon' src={searchIcon} mode='aspectFit' />
        <Input
          className='dynamics-header__search-input'
          placeholder='搜索动态'
          value={searchKeyword}
          onInput={onSearchInput}
          placeholderClass='dynamics-header__search-placeholder'
        />
      </View>
    </View>
  )
}

export default DynamicsHeader
