/**
 * Banner 组件工具函数
 */
import Taro from '@tarojs/taro'
import { BannerItem, LinkType, LinkConfig } from './types'

/**
 * 解析跳转链接类型
 * @param url 跳转链接
 * @returns 链接配置
 */
export const parseLinkUrl = (url: string): LinkConfig => {
  if (!url) {
    throw new Error('URL 不能为空')
  }

  // 外部链接
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return {
      type: LinkType.EXTERNAL,
      url
    }
  }

  // tabbar 页面
  if (url.includes('/pages/tabbar/') || isTabBarPage(url)) {
    return {
      type: LinkType.SWITCH_TAB,
      url: url.split('?')[0] // tabbar 页面不支持参数
    }
  }

  // 重定向页面
  if (url.includes('redirect=true')) {
    return {
      type: LinkType.REDIRECT,
      url
    }
  }

  // 默认为普通页面跳转
  return {
    type: LinkType.NAVIGATE,
    url
  }
}

/**
 * 判断是否为 tabbar 页面
 * @param url 页面路径
 * @returns 是否为 tabbar 页面
 */
export const isTabBarPage = (url: string): boolean => {
  const tabBarPages = [
    '/pages/index/index',
    '/pages/lawyer/index', 
    '/pages/case/index',
    '/pages/mine/index'
  ]
  
  const pagePath = url.split('?')[0]
  return tabBarPages.includes(pagePath)
}

/**
 * 执行页面跳转
 * @param linkConfig 链接配置
 */
export const navigateToPage = async (linkConfig: LinkConfig): Promise<void> => {
  try {
    switch (linkConfig.type) {
      case LinkType.EXTERNAL:
        // 外部链接，复制到剪贴板
        await Taro.setClipboardData({
          data: linkConfig.url
        })
        Taro.showToast({
          title: '链接已复制',
          icon: 'success'
        })
        break

      case LinkType.SWITCH_TAB:
        // tabbar 页面跳转
        await Taro.switchTab({
          url: linkConfig.url
        })
        break

      case LinkType.REDIRECT:
        // 重定向跳转
        await Taro.redirectTo({
          url: linkConfig.url
        })
        break

      case LinkType.NAVIGATE:
      default:
        // 普通页面跳转
        await Taro.navigateTo({
          url: linkConfig.url
        })
        break
    }
  } catch (error) {
    console.error('页面跳转失败:', error)
    Taro.showToast({
      title: '跳转失败',
      icon: 'error'
    })
    throw error
  }
}

/**
 * 处理 Banner 点击事件
 * @param item Banner 项目
 * @param index 索引
 * @param customHandler 自定义处理函数
 */
export const handleBannerClick = async (
  item: BannerItem,
  index: number,
  customHandler?: (item: BannerItem, index: number) => void
): Promise<void> => {
  // 如果有自定义处理函数，优先执行
  if (customHandler) {
    customHandler(item, index)
    return
  }

  // 如果没有跳转链接，不执行任何操作
  if (!item.linkUrl) {
    return
  }

  // 解析并执行跳转
  try {
    const linkConfig = parseLinkUrl(item.linkUrl)
    await navigateToPage(linkConfig)
  } catch (error) {
    console.error('Banner 点击处理失败:', error)
  }
}

/**
 * 预加载图片
 * @param imageUrls 图片URL数组
 * @returns Promise
 */
export const preloadImages = (imageUrls: string[]): Promise<void[]> => {
  const promises = imageUrls.map(url => {
    return new Promise<void>((resolve, reject) => {
      const image = new Image()
      image.onload = () => resolve()
      image.onerror = () => reject(new Error(`图片加载失败: ${url}`))
      image.src = url
    })
  })

  return Promise.allSettled(promises).then(results => {
    const successCount = results.filter(result => result.status === 'fulfilled').length
    console.log(`图片预加载完成: ${successCount}/${imageUrls.length}`)
    return results.map(() => undefined)
  })
}

/**
 * 生成 Banner 数据的唯一 key
 * @param item Banner 项目
 * @param index 索引
 * @returns 唯一 key
 */
export const generateBannerKey = (item: BannerItem, index: number): string => {
  return item.id ? String(item.id) : `banner-${index}-${item.imageUrl.slice(-10)}`
}

/**
 * 验证 Banner 数据
 * @param banners Banner 数据数组
 * @returns 验证结果
 */
export const validateBanners = (banners: BannerItem[]): {
  isValid: boolean
  errors: string[]
} => {
  const errors: string[] = []

  if (!Array.isArray(banners)) {
    errors.push('banners 必须是数组')
    return { isValid: false, errors }
  }

  if (banners.length === 0) {
    errors.push('banners 数组不能为空')
    return { isValid: false, errors }
  }

  banners.forEach((item, index) => {
    if (!item.imageUrl) {
      errors.push(`第 ${index + 1} 个 banner 缺少 imageUrl`)
    }

    if (item.imageUrl && typeof item.imageUrl !== 'string') {
      errors.push(`第 ${index + 1} 个 banner 的 imageUrl 必须是字符串`)
    }

    if (item.linkUrl && typeof item.linkUrl !== 'string') {
      errors.push(`第 ${index + 1} 个 banner 的 linkUrl 必须是字符串`)
    }
  })

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 格式化 Banner 数据
 * @param rawData 原始数据
 * @returns 格式化后的 Banner 数据
 */
export const formatBannerData = (rawData: any[]): BannerItem[] => {
  if (!Array.isArray(rawData)) {
    return []
  }

  return rawData
    .filter(item => item && item.imageUrl) // 过滤无效数据
    .map((item, index) => ({
      id: item.id || index,
      imageUrl: item.imageUrl,
      linkUrl: item.linkUrl || item.link || item.url,
      title: item.title || item.name,
      description: item.description || item.desc || item.subtitle
    }))
}

/**
 * 获取图片尺寸信息
 * @param imageUrl 图片URL
 * @returns 图片尺寸
 */
export const getImageInfo = (imageUrl: string): Promise<{
  width: number
  height: number
  path: string
}> => {
  return new Promise((resolve, reject) => {
    Taro.getImageInfo({
      src: imageUrl,
      success: (res) => {
        resolve({
          width: res.width,
          height: res.height,
          path: res.path
        })
      },
      fail: reject
    })
  })
}
