/**
 * 律师简介组件样式
 */

.lawyer-profile {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

  &__title {
    font-size: 32rpx;
    font-weight: 600;
    color: #000000;
    display: block;
  }

  &__content {
    margin-top: 30rpx;
    max-height: 520rpx;
    overflow: hidden;
    transition: max-height 0.3s ease;

    &--expanded {
      max-height: none;
    }
  }

  &__text {
    font-size: 28rpx;
    color: #333333;
    line-height: 1.6;
    display: block;
  }

  &__toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20rpx;
    gap: 10rpx;
    cursor: pointer;
    transition: all 0.2s ease;

    &:active {
      background-color: #F5F5F5;
      border-radius: 10rpx;
    }
  }

  &__icon {
    width: 45rpx;
    height: 45rpx;
  }

  &__toggle-text {
    font-weight: 400;
    font-size: 24rpx;
    color: #727E8C;
    line-height: 42rpx;
  }
}
