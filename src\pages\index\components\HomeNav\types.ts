/**
 * HomeNav 导航组件类型定义
 */

// 导航项目接口
export interface NavItem {
  // 唯一标识
  id: string | number
  // 图标路径
  iconSrc: string
  // 标题
  title: string
  // 跳转链接 (可选)
  linkUrl?: string
}

// HomeNav 组件属性接口
export interface HomeNavProps {
  // 导航项目列表
  navItems?: NavItem[]
  // 自定义样式类名
  className?: string
  // 点击事件回调
  onItemClick?: (item: NavItem, index: number) => void
  // 是否禁用点击
  disabled?: boolean
}

// 默认导航项目配置
export interface DefaultNavConfig {
  // 图标文件名前缀
  iconPrefix: string
  // 图标文件扩展名
  iconExtension: string
  // 标题列表
  titles: string[]
}
