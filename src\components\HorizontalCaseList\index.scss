/**
 * 横向案例组件样式
 */

.horizontal-case-list {
  width: 100%;

  &__loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200rpx;
    color: #999;
    font-size: 28rpx;
  }

  &__empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 60rpx 40rpx;
    text-align: center;
  }

  &__empty-icon {
    font-size: 60rpx;
    margin-bottom: 16rpx;
    opacity: 0.6;
  }

  &__empty-title {
    font-size: 28rpx;
    color: #666;
    font-weight: 500;
    margin-bottom: 12rpx;
  }

  &__empty-desc {
    font-size: 22rpx;
    color: #999;
    line-height: 1.4;
    max-width: 400rpx;
  }

  &__scroll-view {
    width: 100%;
    padding-right: 30rpx;
    white-space: nowrap;
  }

  &__container {
    display: flex;
    flex-direction: row;
    padding: 0 30rpx;
  }
}

// 案例卡片样式
.horizontal-case-card {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  justify-content: space-between;
  width: 520rpx;
  height: 243rpx;
  background: #FAE8CD;
  box-shadow: 0rpx 2rpx 48rpx 0rpx rgba(0, 0, 0, 0.04);
  border-radius: 14rpx;
  padding: 30rpx;
  margin-right: 30rpx;
  vertical-align: top;
  white-space: normal;
  box-sizing: border-box;
  cursor: pointer;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.98);
  }

  &:last-child {
    margin-right: 0;
  }

  &__title {
    font-weight: 600;
    font-size: 28rpx;
    color: #000000;
    line-height: 36rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    margin-bottom: 20rpx;
  }

  &__meta {
    display: flex;
    align-items: center;
    margin-top: auto;
  }

  &__category {
    font-weight: 600;
    font-size: 20rpx;
    color: #BD8A4F;
    line-height: 25rpx;
    margin-right: 20rpx;
  }

  &__clock-icon {
    width: 25rpx;
    height: 25rpx;
    margin-right: 10rpx;
  }

  &__time {
    font-weight: 600;
    font-size: 20rpx;
    color: #000000;
    line-height: 25rpx;
  }
}
