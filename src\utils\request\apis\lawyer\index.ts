/**
 * 律师相关接口
 * 基于 api.json 接口文档定义
 */
import request from '../../index'

// 律师相关接口
export const lawyerApi = {
  // 获取律师列表
  getLawyerList: (params?: LawyerAPI.GetLawyerListRequest) =>
    request.get<LawyerAPI.GetLawyerListResponse>('/mini/find-lawyer/list', params),

  // 获取律师详情
  getLawyerDetail: (lawyerId: number) =>
    request.get<LawyerAPI.GetLawyerDetailResponse>(`/mini/find-lawyer/${lawyerId}/detail`),
}
