/**
 * 律师相关接口
 * 基于 api.json 接口文档定义
 */
import request from '../../index'
import { BaseResponse } from '../../types'

// 律师相关接口
export const lawyerApi = {
  // 获取律师列表（基于 /mini/find-lawyer/list）
  getLawyerList: (params?: LawyerAPI.GetLawyerListRequest): Promise<BaseResponse<LawyerAPI.GetLawyerListResponse>> =>
    request.get<LawyerAPI.GetLawyerListResponse>('/mini/find-lawyer/list', params),

  // 获取律师详情信息（基于 /mini/find-lawyer/:lawyerId/detail-info）
  getLawyerDetailInfo: (lawyerId: number): Promise<BaseResponse<LawyerAPI.GetLawyerDetailInfoResponse>> =>
    request.get<LawyerAPI.GetLawyerDetailInfoResponse>(`/mini/find-lawyer/${lawyerId}/detail-info`),

  // 获取律师统计数据（基于 /mini/find-lawyer/:lawyerId/detail-lawyer-count）
  getLawyerCountData: (lawyerId: number): Promise<BaseResponse<LawyerAPI.GetLawyerCountDataResponse>> =>
    request.get<LawyerAPI.GetLawyerCountDataResponse>(`/mini/find-lawyer/${lawyerId}/detail-lawyer-count`),

  // 获取律师内容统计（基于 /mini/find-lawyer/:lawyerId/detail-content-count）
  getLawyerContentNum: (lawyerId: number): Promise<BaseResponse<LawyerAPI.GetLawyerContentNumResponse>> =>
    request.get<LawyerAPI.GetLawyerContentNumResponse>(`/mini/find-lawyer/${lawyerId}/detail-content-count`),

  // 关注律师（基于 /mini/find-lawyer/:lawyerId/follow）
  followLawyer: (lawyerId: number): Promise<BaseResponse<LawyerAPI.FollowLawyerResponse>> =>
    request.post<LawyerAPI.FollowLawyerResponse>(`/mini/find-lawyer/${lawyerId}/follow`, { lawyerId }),

  // 获取律师关注列表（基于 /mini/owner-center-lawyer/lawyer-follow/list）
  getOwnerLawyerFollowList: (params?: LawyerAPI.GetOwnerLawyerFollowListRequest): Promise<BaseResponse<LawyerAPI.GetOwnerLawyerFollowListResponse>> =>
    request.get<LawyerAPI.GetOwnerLawyerFollowListResponse>('/mini/owner-center-lawyer/lawyer-follow/list', params),

  // 获取律师个人信息（基于 /mini/owner-center-lawyer/lawyer-info）
  getOwnerLawyerInfo: (): Promise<BaseResponse<LawyerAPI.GetOwnerLawyerInfoResponse>> =>
    request.get<LawyerAPI.GetOwnerLawyerInfoResponse>('/mini/owner-center-lawyer/lawyer-info'),
}
