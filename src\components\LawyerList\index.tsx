/**
 * 律师列表组件
 * 
 * 展示最多3个律师卡片，支持外部传入筛选条件
 */
import React, { useState, useEffect } from 'react'
import { View, Image, Text } from '@tarojs/components'
import { lawyer<PERSON><PERSON> } from '@/utils/request/apis/lawyer'
import { navigateToPage, showLoading, hideLoading } from '@/utils'

// 导入图片资源
import layerCardBg from '@/assets/images/layer_card_bg.png'
import layerLevel1Icon from '@/assets/images/layer-level-icon/layer_1.png'
import layerLevel2Icon from '@/assets/images/layer-level-icon/layer_2.png'
import layerLevel3Icon from '@/assets/images/layer-level-icon/layer_3.png'
import layerLevel4Icon from '@/assets/images/layer-level-icon/layer_4.png'
import layerLevel5Icon from '@/assets/images/layer-level-icon/layer_5.png'

import { LawyerListProps } from './types'
import './index.scss'


// 律师等级图标映射（已移到函数内部）

const LawyerList: React.FC<LawyerListProps> = ({
  filterParams,
  className = ''
}) => {
  // 律师列表数据
  const [lawyers, setLawyers] = useState<LawyerAPI.LawyerInfo[]>([])
  // 加载状态
  const [isLoading, setIsLoading] = useState(false)

  // 获取律师等级图标
  const getLevelIcon = (lawyerLevel: number): string => {
    const levelIconMap = {
      1: layerLevel1Icon,
      2: layerLevel2Icon,
      3: layerLevel3Icon,
      4: layerLevel4Icon,
      5: layerLevel5Icon
    }
    return levelIconMap[lawyerLevel] || layerLevel1Icon
  }

  // 加载律师列表数据
  const loadLawyers = async () => {
    try {
      setIsLoading(true)
      // 使用toast阻止用户操作
      showLoading()
      const params = {
        ...filterParams,
        page: 1,
        pageSize: 3 // 最多展示3个律师
      }

      const response = await lawyerApi.getLawyerList(params)
      if (response.success && response.data) {
        setLawyers(response.data.list)
      }
         hideLoading()
    } catch (error) {
      const mockList: LawyerAPI.LawyerInfo[] = [
        {
          "id": 1,
          "userId": 4,
          "province": "湖南",
          "city": "长沙",
          "district": "雨花",
          "name": "潭美丽",
          "personalProfile": "专业从事劳动纠纷、合同纠纷等法律事务",
          "figurePhotoUrl": "https://file.tinglifangs.com/pro/headPortrait/1655790156707.jpeg?x-oss-process=image/resize,w_600,m_lfit",
          "lawFirm": "湖南某律师事务所",
          "lawFirmAddress": "湖南省长沙市雨花区某街道123号",
          "lawyerLevel": 1,
          "lawyerField": [
            { "id": 1, "name": "劳动纠纷" },
            { "id": 2, "name": "合同纠纷" }
          ],
          "createdAt": "2025-06-21 12:28:53"
        },
        {
          "id": 2,
          "userId": 1,
          "province": "广东",
          "city": "深圳",
          "district": "南山",
          "name": "谢大帅",
          "personalProfile": "资深律师，擅长公司法务、知识产权等领域",
          "figurePhotoUrl": "https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/e17b23da73464f759ac268736a9e232d_mergeImage.png",
          "lawFirm": "广东某知名律师事务所",
          "lawFirmAddress": "广东省深圳市南山区某大厦20楼",
          "lawyerLevel": 4,
          "lawyerField": [
            { "id": 3, "name": "公司法务" },
            { "id": 4, "name": "知识产权" }
          ],
          "createdAt": "2025-06-20 22:16:10"
        }
      ]
      setLawyers(mockList)
         hideLoading()
      console.error('加载律师列表失败:', error)
    } finally {
      setIsLoading(false)
    }
  }



  // 处理律师卡片点击 - 跳转到律师详情页
  const handleLawyerClick = (lawyer: LawyerAPI.LawyerInfo) => {
    navigateToPage(`/subpackages/detail/lawyer/index?userId=${lawyer.userId}`)
  }

  // 组件挂载时加载数据
  useEffect(() => {
    loadLawyers()
  }, [filterParams])

  return (
    <View className={`lawyer-list ${className}`}>
      {isLoading ? (
        <View className='lawyer-list__loading'>
          <Text>加载中...</Text>
        </View>
      ) : (
        <>
          {lawyers.length > 0 ? (
            <View className='lawyer-list__container'>
              {lawyers.map((lawyer, index) => (
                <View
                  key={lawyer.userId}
                  className='lawyer-card'
                  style={{
                    backgroundImage: `url(${layerCardBg})`,
                    marginBottom: index < lawyers.length - 1 ? '20rpx' : '0'
                  }}
                  onClick={() => handleLawyerClick(lawyer)}
                >
                  <View className='lawyer-card__avatar'>
                    <Image
                      className='lawyer-card__avatar-img'
                      src={lawyer.figurePhotoUrl || '/assets/images/default-avatar.png'}
                      mode='aspectFill'
                    />
                  </View>
                  <View className='lawyer-card__info'>
                    <View className='lawyer-card__top'>
                      <View className='lawyer-card__name-row'>
                        <Text className='lawyer-card__name'>{lawyer.name}</Text>
                        <Image
                          className='lawyer-card__level-icon'
                          src={getLevelIcon(lawyer.lawyerLevel)}
                          mode='aspectFit'
                        />
                      </View>
                      <Text className='lawyer-card__description'>
                        {lawyer.personalProfile}
                      </Text>
                    </View>
                    <View className='lawyer-card__bottom'>
                      <Text className='lawyer-card__city'>
                        {lawyer.province}{lawyer.city}{lawyer.district}
                      </Text>
                      <Text className='lawyer-card__office'>
                        {lawyer.lawFirm}
                      </Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <View className='lawyer-list__empty'>
              <Text className='lawyer-list__empty-title'>暂无律师信息</Text>
              <Text className='lawyer-list__empty-desc'>当前筛选条件下没有找到相关律师，请尝试调整搜索条件</Text>
            </View>
          )}
        </>
      )}
    </View>
  )
}

// 导出组件
export default LawyerList

// 导出类型
export type { LawyerListProps } from './types'
