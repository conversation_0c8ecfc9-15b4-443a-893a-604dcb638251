/**
 * Service 客服组件
 * 
 * 漂浮在页面右下角的客服按钮，点击可跳转到微信客服
 */
import React from 'react'
import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
// 导入客服图标
import headsetIcon from '@/assets/images/common-icon/headset_outline.png'
import { ServiceProps } from './types'
import './index.scss'

const Service: React.FC<ServiceProps> = ({
  className = '',
  visible = true
}) => {

  // 处理客服按钮点击
  const handleServiceClick = () => {
    try {
      // 使用 Taro API 跳转到微信客服
      Taro.openCustomerServiceChat({
        extInfo: {
          url: 'https://work.weixin.qq.com/kfid/kfc123456789' // 这里需要替换为实际的客服链接
        },
        corpId: 'your_corp_id', // 这里需要替换为实际的企业ID
        success: (res) => {
          console.log('打开客服成功:', res)
        },
        fail: (err) => {
          console.error('打开客服失败:', err)
          // 如果微信客服不可用，可以提供备用方案
          Taro.showToast({
            title: '客服暂时不可用',
            icon: 'none',
            duration: 2000
          })
        }
      })
    } catch (error) {
      console.error('客服功能异常:', error)
      // 备用方案：显示联系方式
      Taro.showModal({
        title: '联系客服',
        content: '请拨打客服电话：************',
        showCancel: true,
        cancelText: '取消',
        confirmText: '拨打',
        success: (res) => {
          if (res.confirm) {
            Taro.makePhoneCall({
              phoneNumber: '************'
            })
          }
        }
      })
    }
  }

  // 如果不可见，则不渲染
  if (!visible) {
    return null
  }

  return (
    <View
      className={`service-widget ${visible ? 'service-widget--visible' : 'service-widget--hidden'} ${className}`}
      onClick={handleServiceClick}
    >
      <Image
        className='service-widget__icon'
        src={headsetIcon}
        mode='aspectFit'
      />
      <Text className='service-widget__text'>客服</Text>
    </View>
  )
}

// 导出组件
export default Service

// 导出类型
export type { ServiceProps } from './types'
