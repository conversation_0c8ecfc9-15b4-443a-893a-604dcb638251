/**
 * 动态列表组件
 */
import React, { useMemo } from 'react'
import { View, Text, Image } from '@tarojs/components'
import clockIcon from '@/assets/images/common-icon/clock.png'
import eyeIcon from '@/assets/images/common-icon/eye.png'

// 组件属性
export interface DynamicsListProps {
  // 动态列表数据
  dynamics: DynamicsAPI.DynamicsListInfo[]
  // 是否加载中
  isLoading: boolean
  // 动态点击回调
  onDynamicsClick: (dynamicsInfo: DynamicsAPI.DynamicsListInfo) => void
  // 时间格式化函数
  formatTime: (timeStr: string) => string
  // 自定义样式类名
  className?: string
}

const DynamicsList: React.FC<DynamicsListProps> = ({
  dynamics,
  isLoading,
  onDynamicsClick,
  formatTime,
  className = ''
}) => {
  // 使用 useMemo 优化动态列表渲染，避免不必要的重新渲染
  const dynamicsItems = useMemo(() => {
    return dynamics.map((dynamicsInfo) => (
      <View
        key={dynamicsInfo.id}
        className='dynamics-item'
        onClick={() => onDynamicsClick(dynamicsInfo)}
      >
        <Text className='dynamics-item__title'>{dynamicsInfo.title}</Text>
        <View className='dynamics-item__meta'>
          <Text className='dynamics-item__category'>{dynamicsInfo.categoryName}</Text>
          <Image className='dynamics-item__info-icon' src={clockIcon} mode='aspectFit' />
          <Text className='dynamics-item__info-text'>{formatTime(dynamicsInfo.createdAt)}</Text>
          <Image className='dynamics-item__info-icon' src={eyeIcon} mode='aspectFit' />
          <Text className='dynamics-item__info-text'>{dynamicsInfo.viewCount}</Text>
        </View>
      </View>
    ))
  }, [dynamics, onDynamicsClick, formatTime])

  // 渲染内容区域
  const renderContent = () => {
    // 如果有数据，显示列表（即使在加载中也显示，避免闪烁）
    if (dynamics.length > 0) {
      return (
        <View className={`dynamics-list__content ${isLoading ? 'dynamics-list__content--loading' : ''}`}>
          {dynamicsItems}
          {/* 加载中时显示半透明遮罩 */}
          {isLoading && (
            <View className='dynamics-list__loading-overlay'>
              <Text className='dynamics-list__loading-text'>更新中...</Text>
            </View>
          )}
        </View>
      )
    }

    // 如果正在加载且没有数据，显示加载状态
    if (isLoading) {
      return (
        <View className='dynamics-list__loading'>
          <Text>加载中...</Text>
        </View>
      )
    }

    // 没有数据且不在加载中，显示空状态
    return (
      <View className='dynamics-list__empty'>
        <Text>暂无动态</Text>
      </View>
    )
  }

  return (
    <View className={`dynamics-list ${className}`}>
      {renderContent()}
    </View>
  )
}

export default DynamicsList
