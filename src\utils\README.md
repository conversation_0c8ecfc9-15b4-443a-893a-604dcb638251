# 全局工具函数库

专为 Taro 微信小程序项目设计的工具函数库，提供常用的工具方法，包括导航、存储、格式化、验证、小程序特有功能等。

## 功能模块

### 🧭 导航相关

- `navigateToPage(url, options?)` - 智能页面跳转
- `navigateBack(delta?)` - 返回上一页
- `redirectTo(url)` - 重定向到指定页面
- `relaunchCurrentPage()` - 重新加载当前页面
- `isTabBarPage(url)` - 判断是否为 tabbar 页面

### 💾 存储相关

- `setStorage(key, data, sync?)` - 设置本地存储
- `getStorage(key, defaultValue?, sync?)` - 获取本地存储
- `removeStorage(key, sync?)` - 移除本地存储
- `clearStorage(sync?)` - 清空本地存储

### 📝 格式化相关

- `formatDate(date, format?)` - 格式化日期
- `formatFileSize(bytes, decimals?)` - 格式化文件大小
- `formatNumber(num, options?)` - 格式化数字
- `formatPhone(phone, separator?)` - 格式化手机号

### ✅ 验证相关

- `validatePhone(phone)` - 验证手机号
- `validateEmail(email)` - 验证邮箱
- `validateIdCard(idCard)` - 验证身份证号
- `validateUrl(url)` - 验证URL

### 🔧 工具函数

- `debounce(func, wait)` - 防抖函数
- `throttle(func, limit)` - 节流函数
- `deepClone(obj)` - 深拷贝
- `generateId(prefix?)` - 生成唯一ID
- `sleep(ms)` - 延迟执行

### 📊 数组相关

- `uniqueArray(arr, key?)` - 数组去重
- `groupBy(arr, key)` - 数组分组
- `sortArray(arr, key, order?)` - 数组排序
- `paginateArray(arr, page, pageSize)` - 数组分页

### 🎯 对象相关

- `deepMerge(target, ...sources)` - 对象深度合并
- `getDeepProperty(obj, path, defaultValue?)` - 获取对象深层属性
- `setDeepProperty(obj, path, value)` - 设置对象深层属性
- `filterObject(obj, predicate)` - 过滤对象属性

### 🔤 字符串相关

- `capitalize(str)` - 首字母大写
- `camelToSnake(str)` - 驼峰转下划线
- `snakeToCamel(str)` - 下划线转驼峰
- `truncate(str, length, suffix?)` - 截断字符串
- `stripHtml(str)` - 移除HTML标签

### 📱 小程序基础功能

- `getSystemInfo()` - 获取系统信息
- `showToast(title, options?)` - 显示Toast
- `showLoading(title?, mask?)` - 显示加载中
- `hideLoading()` - 隐藏加载中
- `showModal(options)` - 显示模态对话框
- `chooseImage(options?)` - 选择图片
- `previewImage(urls, current?)` - 预览图片
- `setClipboardData(data, showToast?)` - 复制到剪贴板

### 🔐 用户授权相关

- `getUserInfo(withCredentials?)` - 获取用户信息
- `getSetting()` - 获取用户授权设置
- `openSetting()` - 打开设置页面

### 📍 位置相关

- `getLocation(type?)` - 获取位置信息
- `chooseLocation()` - 选择位置
- `openLocation(options)` - 打开位置

### 📷 设备功能

- `scanCode(options?)` - 扫码
- `makePhoneCall(phoneNumber)` - 拨打电话
- `vibrateShort(type?)` - 短振动
- `vibrateLong()` - 长振动
- `getDeviceInfo()` - 获取设备信息
- `getWindowInfo()` - 获取窗口信息

### 🌐 网络相关

- `getNetworkType()` - 获取网络类型
- `onNetworkStatusChange(callback)` - 监听网络状态变化

### 📄 页面相关

- `getCurrentPages()` - 获取当前页面栈
- `getCurrentPagePath()` - 获取当前页面路径
- `getCurrentPageOptions()` - 获取当前页面参数
- `setNavigationBarTitle(title)` - 设置页面标题
- `setNavigationBarColor(options)` - 设置导航栏颜色
- `showNavigationBarLoading()` - 显示导航栏加载动画
- `hideNavigationBarLoading()` - 隐藏导航栏加载动画

### 📁 文件相关

- `getImageInfo(src)` - 获取图片信息
- `downloadFile(url, filePath?)` - 下载文件
- `uploadFile(options)` - 上传文件
- `saveFile(tempFilePath)` - 保存文件到本地
- `getSavedFileList()` - 获取已保存的文件列表
- `getFileInfo(filePath)` - 获取本地文件信息
- `removeSavedFile(filePath)` - 删除本地文件
- `saveImageToPhotosAlbum(filePath)` - 保存图片到相册

### 🎵 音频相关

- `playVoice(filePath)` - 播放音频
- `pauseVoice()` - 暂停播放音频
- `stopVoice()` - 停止播放音频

### 🔧 单位转换

- `rpxToPx(rpx)` - rpx 转 px
- `pxToRpx(px)` - px 转 rpx

### 📱 应用相关

- `isDev()` - 检查是否为开发环境
- `isProd()` - 检查是否为生产环境
- `getAppVersion()` - 获取小程序版本信息
- `checkForUpdate()` - 检查小程序更新

## 使用示例

### 导航跳转

```typescript
import { navigateToPage, isTabBarPage } from '@/utils'

// 普通页面跳转
await navigateToPage('/pages/detail/index?id=123')

// tabbar 页面跳转
await navigateToPage('/pages/index/index')

// 外部链接（会复制到剪贴板）
await navigateToPage('https://www.example.com')

// 检查是否为 tabbar 页面
const isTabBar = isTabBarPage('/pages/index/index')
```

### 本地存储

```typescript
import { setStorage, getStorage, removeStorage } from '@/utils'

// 存储数据
await setStorage('userInfo', { id: 123, name: '张三' })

// 获取数据（有默认值）
const userInfo = await getStorage('userInfo', { id: 0, name: '游客' })

// 获取数据（无默认值，可能为 null）
const userInfo2 = await getStorage<UserInfo>('userInfo')

// 类型安全的存储
interface UserInfo {
  id: number
  name: string
  email: string
}

const defaultUser: UserInfo = { id: 0, name: '游客', email: '' }
const user = await getStorage('userInfo', defaultUser) // 类型为 UserInfo

// 移除数据
await removeStorage('userInfo')
```

### 数据格式化

```typescript
import { formatDate, formatPhone, formatFileSize } from '@/utils'

// 格式化日期
const dateStr = formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss')

// 格式化手机号
const phoneStr = formatPhone('13800138000') // 138 0013 8000

// 格式化文件大小
const sizeStr = formatFileSize(1024 * 1024) // 1 MB
```

### 数据验证

```typescript
import { validatePhone, validateEmail, validateUrl } from '@/utils'

// 验证手机号
const isValidPhone = validatePhone('13800138000')

// 验证邮箱
const isValidEmail = validateEmail('<EMAIL>')

// 验证URL
const isValidUrl = validateUrl('https://www.example.com')
```

### 数组操作

```typescript
import { uniqueArray, groupBy, sortArray } from '@/utils'

// 数组去重
const uniqueNumbers = uniqueArray([1, 2, 2, 3, 3])

// 对象数组去重
const uniqueUsers = uniqueArray(users, 'id')

// 数组分组
const groupedUsers = groupBy(users, 'department')

// 数组排序
const sortedUsers = sortArray(users, 'age', 'desc')
```

### 工具函数

```typescript
import { debounce, throttle, deepClone, sleep } from '@/utils'

// 防抖搜索
const debouncedSearch = debounce((keyword: string) => {
  console.log('搜索:', keyword)
}, 300)

// 节流滚动
const throttledScroll = throttle(() => {
  console.log('滚动事件')
}, 100)

// 深拷贝
const clonedObj = deepClone(originalObj)

// 延迟执行
await sleep(1000) // 等待1秒
```

### 小程序API

```typescript
import { showToast, showModal, chooseImage, setClipboardData } from '@/utils'

// 显示提示
showToast('操作成功', { icon: 'success' })

// 显示确认框
const result = await showModal({
  title: '确认',
  content: '确定要删除吗？'
})

// 选择图片
const images = await chooseImage({ count: 3 })

// 复制到剪贴板
await setClipboardData('要复制的内容')
```

## 最佳实践

### 1. 统一导入

```typescript
// 推荐：按需导入
import { navigateToPage, showToast, formatDate } from '@/utils'

// 避免：全量导入
import * as utils from '@/utils'
```

### 2. 错误处理

```typescript
try {
  await navigateToPage('/pages/detail/index')
} catch (error) {
  console.error('跳转失败:', error)
  showToast('跳转失败', { icon: 'error' })
}
```

### 3. 类型安全

```typescript
// ✅ 推荐：提供默认值，确保返回值类型
interface UserInfo {
  id: number
  name: string
}

const defaultUser: UserInfo = { id: 0, name: '游客' }
const userInfo = await getStorage('userInfo', defaultUser) // 类型为 UserInfo

// ✅ 可选：无默认值，需要处理 null 情况
const userInfo2 = await getStorage<UserInfo>('userInfo') // 类型为 UserInfo | null
if (userInfo2) {
  console.log(userInfo2.name) // 类型安全
}

// ❌ 避免：不提供默认值且不处理 null
const userInfo3 = await getStorage<UserInfo>('userInfo')
console.log(userInfo3.name) // 可能报错，userInfo3 可能为 null
```

### 4. 性能优化

```typescript
// 使用防抖优化搜索
const debouncedSearch = debounce(searchFunction, 300)

// 使用节流优化滚动事件
const throttledScroll = throttle(scrollHandler, 100)
```

## 注意事项

1. **异步函数**: 大部分函数都是异步的，记得使用 `await` 或 `.then()`
2. **错误处理**: 建议使用 `try-catch` 包装异步调用
3. **类型检查**: 使用 TypeScript 时注意类型定义
4. **性能考虑**: 避免在循环中调用复杂的工具函数
5. **兼容性**: 部分功能依赖小程序API，在其他环境中可能不可用

## 扩展指南

如需添加新的工具函数：

1. 在 `src/utils/index.ts` 中添加函数实现
2. 在 `src/utils/example.ts` 中添加使用示例
3. 在此 README 中更新文档
4. 确保函数有完整的 TypeScript 类型定义
5. 添加必要的错误处理和参数验证
