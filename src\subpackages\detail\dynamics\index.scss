/**
 * 律师动态详情页面样式
 */

.dynamics-detail {
  &__content {
    padding: 30rpx;
  }
}

// 动态标题
.dynamics-title {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  line-height: 48rpx;
  margin-bottom: 30rpx;
  word-break: break-word;
}

// 动态元信息
.dynamics-meta {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

  &__row {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__label {
    font-size: 28rpx;
    color: #666;
    width: 140rpx;
    flex-shrink: 0;
    line-height: 32rpx;
  }

  &__value {
    font-size: 28rpx;
    color: #333;
    flex: 1;
    line-height: 32rpx;
  }
}

// 动态内容
.dynamics-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

  &__title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
    line-height: 36rpx;
  }

  &__text {
    font-size: 30rpx;
    color: #333;
    line-height: 44rpx;
    word-break: break-word;
    white-space: pre-wrap;
  }
}

// 操作按钮
.dynamics-actions {
  display: flex;
  gap: 30rpx;
  padding: 20rpx 0;

  &__btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 600;

    &::after {
      border: none;
    }

    // 主要按钮样式
    &[type="primary"] {
      background: #BD8A4F;
      border: none;
    }

    // 次要按钮样式
    &[plain] {
      background: transparent;
      color: #BD8A4F;
      border: 2rpx solid #BD8A4F;
    }
  }
}
