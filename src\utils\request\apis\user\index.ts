/**
 * 用户相关接口
 */
import request from '../../index'

// 用户相关接口
export const userApi = {
  // 登录
  login: (data: UserAPI.LoginRequest) =>
    request.post<UserAPI.LoginResponse>('/auth/login', data),

  // 注册
  register: (data: UserAPI.RegisterRequest) =>
    request.post<UserAPI.LoginResponse>('/auth/register', data),

  // 获取用户信息
  getUserInfo: () =>
    request.get<UserAPI.UserInfo>('/user/info'),

  // 更新用户信息
  updateUserInfo: (data: UserAPI.UpdateUserInfoRequest) =>
    request.put<UserAPI.UserInfo>('/user/info', data),

  // 退出登录
  logout: () =>
    request.post('/auth/logout'),

  // 刷新token
  refreshToken: () =>
    request.post<UserAPI.RefreshTokenResponse>('/auth/refresh'),
}
