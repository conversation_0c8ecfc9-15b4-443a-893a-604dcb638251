/**
 * 文章详情页面样式
 */

.article-detail {
  &__content {
    padding: 30rpx;
  }
}

// 文章标题
.article-title {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  line-height: 48rpx;
  margin-bottom: 30rpx;
  word-break: break-word;
}

// 文章元信息
.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;

  &__author {
    display: flex;
    align-items: center;
    gap: 20rpx;
  }

  &__avatar {
    width: 60rpx;
    height: 60rpx;
    border-radius: 30rpx;
  }

  &__name {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    line-height: 32rpx;
  }

  &__info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5rpx;
  }

  &__time {
    font-size: 24rpx;
    color: #999;
    line-height: 28rpx;
  }

  &__stats {
    font-size: 22rpx;
    color: #999;
    line-height: 26rpx;
  }
}

// 文章标签
.article-tags {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 40rpx;
  flex-wrap: wrap;

  &__category {
    background: #BD8A4F;
    color: #fff;
    font-size: 22rpx;
    padding: 8rpx 16rpx;
    border-radius: 12rpx;
    line-height: 26rpx;
  }

  &__tag {
    background: #f5f5f5;
    color: #666;
    font-size: 22rpx;
    padding: 8rpx 16rpx;
    border-radius: 12rpx;
    line-height: 26rpx;
  }
}

// 文章内容
.article-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

  &__text {
    font-size: 30rpx;
    color: #333;
    line-height: 44rpx;
    word-break: break-word;
    white-space: pre-wrap;
  }
}

// 操作按钮
.article-actions {
  display: flex;
  justify-content: center;
  gap: 30rpx;
  padding: 20rpx 0;

  &__btn {
    background: #f8f8f8;
    color: #666;
    border: 2rpx solid #e0e0e0;
    border-radius: 40rpx;
    font-size: 26rpx;
    padding: 16rpx 32rpx;

    &::after {
      border: none;
    }

    &:active {
      background: #e8e8e8;
    }
  }
}
