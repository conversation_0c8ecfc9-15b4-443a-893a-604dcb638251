import React from 'react'
import { View } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { uploadApi } from '@/utils/request/apis/upload'
import PageLayout from '@/components/PageLayout'
import './index.scss'

const LawyerEntry: React.FC = () => {
  const [url, setUrl] = React.useState('')
  const handleUpload = async () => {
    try {
      const { tempFilePaths } = await Taro.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera']
      })
      const res = await uploadApi.uploadImage(tempFilePaths[0])
      setUrl(res.data.url)
      console.log('上传成功:', res)
    } catch (error) {
      console.error('上传失败:', error)
    }
  }
  return (
    <PageLayout title='律师入驻'>
      <View className='lawyer-entry'>
        <View onClick={handleUpload} >上传测试</View>
      </View>
      <View>返回图片地赴：{url}</View>
    </PageLayout>
  )
}

export default LawyerEntry
