/**
 * 律师列表组件样式
 */

.lawyer-list {
  width: 100%;

  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 80rpx 0;

    text {
      font-size: 28rpx;
      color: #999999;
    }
  }

  &__container {
    width: 100%;
  }

  &__empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 80rpx 40rpx;
    text-align: center;
  }

  &__empty-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
    opacity: 0.6;
  }

  &__empty-title {
    font-size: 32rpx;
    color: #666;
    font-weight: 500;
    margin-bottom: 16rpx;
  }

  &__empty-desc {
    font-size: 24rpx;
    color: #999;
    line-height: 1.5;
    max-width: 500rpx;
  }


}

// 律师卡片样式
.lawyer-card {
  position: relative;
  width: 690rpx;
  height: 320rpx;
  padding: 20rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: stretch;
  cursor: pointer;
  transition: all 0.3s ease;
  box-sizing: border-box;

  &__bg {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
  }

  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }

  // 律师头像
  &__avatar {
    width: 200rpx;
    height: 280rpx;
    margin-right: 30rpx;
    border-radius: 12rpx;
    overflow: hidden;
    flex-shrink: 0;

    &-img {
      width: 100%;
      height: 100%;
      border-radius: 12rpx;
    }
  }

  // 律师信息区域
  &__info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 280rpx;
    padding: 10rpx 0 20rpx 0;
    overflow: hidden;
  }

  // 上半部分：姓名和简介
  &__top {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  // 律师姓名行
  &__name-row {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
  }

  // 律师姓名
  &__name {
    margin-right: 12rpx;
    font-weight: 600;
    font-size: 30rpx;
    color: #000000;
    line-height: 42rpx; // 与容器高度一致
    display: flex;
    align-items: center;
  }

  // 律师等级图标
  &__level-icon {
    padding-top: 10rpx;
    width: 88rpx;
    height: 30rpx;
    flex-shrink: 0;
    vertical-align: middle;
  }

  // 律师简介
  &__description {
    line-height: 1.5;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    font-weight: 600;
    font-size: 24rpx;
    color: #282828;
    line-height: 32rpx;
    text-align: left;
    font-style: normal;
  }

  // 下半部分：地区和律所
  &__bottom {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }

  // 执业地区
  &__city {
    font-size: 20rpx;
    color: #000000;
    line-height: 28rpx;
  }

  // 所属律所
  &__office {
    font-size: 20rpx;
    color: #000000;
    line-height: 28rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .lawyer-card {
    width: 100%;
    padding: 16rpx;

    &__avatar {
      width: 160rpx;
      height: 240rpx;
      margin-right: 24rpx;
    }

    &__info {
      height: 240rpx;
    }

    &__name {
      font-size: 30rpx;
    }

    &__description {
      font-size: 24rpx;
      -webkit-line-clamp: 2;
    }

    &__city,
    &__office {
      font-size: 22rpx;
    }
  }
}