/**
 * 首页样式
 */

// 首页 Banner 样式
.home-banner {
  width: 100%;
  margin-bottom: 32rpx;

  // 确保 Banner 在页面顶部紧贴
  .banner__image {
    border-radius: 0;
  }

  // 文字覆盖层样式优化
  .banner__overlay {
    background: linear-gradient(to top,
        rgba(0, 0, 0, 0.7) 0%,
        rgba(0, 0, 0, 0.3) 60%,
        transparent 100%);
    padding: 40rpx 32rpx;
  }

  .banner__title {
    font-size: 36rpx;
    font-weight: 700;
    margin-bottom: 12rpx;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
  }

  .banner__description {
    font-size: 28rpx;
    opacity: 0.95;
    text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.5);
  }
}

// 首页内容区域 - 需要内边距的部分
.home-content-padded {
  padding: 30rpx;
}

.home-lawyer-list,
.home-case-list {
  display: flex;
  flex-direction: column;

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__title {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    &-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 12rpx;
    }

    &-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #000000;
    }
  }

  &__more {
    text-align: right;
    margin-bottom: 24rpx;

    text {
      font-size: 28rpx;
      color: #828D99;
    }
  }
}

.home-case-list {
  &__header {
    padding: 0 30rpx;
  }
}