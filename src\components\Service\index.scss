/**
 * Service 客服组件样式
 */

.service-widget {
  position: fixed;
  right: 25rpx;
  bottom: 65rpx;
  width: 88rpx;
  height: 88rpx;
  background: #fff;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  z-index: 999;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
  }

  &:hover {
    box-shadow: 0 6rpx 25rpx rgba(0, 0, 0, 0.2);
  }

  &__icon {
    width: 40rpx;
    height: 40rpx;
    margin-bottom: 5rpx;
  }

  &__text {
    font-weight: 600;
    font-size: 16rpx;
    color: #000000;
    line-height: 18rpx;
    text-align: center;
  }

  // 隐藏状态
  &--hidden {
    opacity: 0;
    pointer-events: none;
    transform: scale(0.8);
  }

  // 显示状态
  &--visible {
    opacity: 1;
    pointer-events: auto;
    transform: scale(1);
  }
}
