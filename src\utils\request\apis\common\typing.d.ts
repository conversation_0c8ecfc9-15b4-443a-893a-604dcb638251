/**
 * 公共模块类型声明
 */

// 引用通用类型
/// <reference path="../../common-types.d.ts" />

declare namespace CommonAPI {
  // 应用配置类型
  interface AppConfig {
    appName: string
    version: string
    apiVersion: string
    features: Record<string, boolean>
    settings: Record<string, any>
  }

  // 城市信息类型
  interface CityInfo extends CommonTypes.BaseEntity {
    name: string
    code: string
    province: string
    level: number
    children?: CityInfo[]
  }

  // 专业领域类型
  interface SpecialtyInfo extends CommonTypes.BaseEntity {
    name: string
    description?: string
    parentId?: string
    level: number
    children?: SpecialtyInfo[]
  }

  // 反馈请求参数
  interface FeedbackRequest {
    content: string
    contact?: string
  }

  // 反馈响应类型
  interface FeedbackResponse extends CommonTypes.BaseApiResponse {
    feedbackId?: string
  }
}
