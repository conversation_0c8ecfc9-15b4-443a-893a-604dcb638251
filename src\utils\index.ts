/**
 * 全局工具函数库
 * 
 * 包含常用的工具方法，如导航、存储、格式化、验证等
 */
import Taro from '@tarojs/taro'

// ==================== 导航相关 ====================

/**
 * 判断是否为 tabbar 页面
 * @param url 页面路径
 * @returns 是否为 tabbar 页面
 */
export const isTabBarPage = (url: string): boolean => {
  const tabBarPages = [
    '/pages/index/index',
    '/pages/lawyer/index',
    '/pages/case/index',
    '/pages/mine/index'
  ]
  
  const pagePath = url.split('?')[0]
  return tabBarPages.includes(pagePath)
}

/**
 * 页面跳转处理
 * @param url 跳转链接
 * @param options 跳转选项
 */
export const navigateToPage = async (
  url: string, 
  options: {
    showToast?: boolean
    toastTitle?: string
  } = {}
): Promise<void> => {
  const { showToast = true, toastTitle = '跳转失败' } = options

  if (!url) {
    throw new Error('跳转链接不能为空')
  }

  try {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      // 外部链接，复制到剪贴板
      await Taro.setClipboardData({
        data: url
      })
      if (showToast) {
        Taro.showToast({
          title: '链接已复制',
          icon: 'success'
        })
      }
    } else if (isTabBarPage(url)) {
      // tabbar 页面
      await Taro.switchTab({
        url: url.split('?')[0] // tabbar 页面不支持参数
      })
    } else {
      // 普通页面
      await Taro.navigateTo({
        url
      })
    }
  } catch (error) {
    console.error('页面跳转失败:', error)
    if (showToast) {
      Taro.showToast({
        title: toastTitle,
        icon: 'error'
      })
    }
    throw error
  }
}

/**
 * 返回上一页
 * @param delta 返回的页面数
 */
export const navigateBack = (delta: number = 1): void => {
  Taro.navigateBack({ delta })
}

/**
 * 重定向到指定页面
 * @param url 页面路径
 */
export const redirectTo = async (url: string): Promise<void> => {
  try {
    await Taro.redirectTo({ url })
  } catch (error) {
    console.error('页面重定向失败:', error)
    throw error
  }
}

/**
 * 重新加载当前页面
 */
export const relaunchCurrentPage = async (): Promise<void> => {
  const pages = Taro.getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const url = `/${currentPage.route}`
  
  try {
    await Taro.reLaunch({ url })
  } catch (error) {
    console.error('页面重新加载失败:', error)
    throw error
  }
}

// ==================== 存储相关 ====================

/**
 * 设置本地存储
 * @param key 存储键
 * @param data 存储数据
 * @param sync 是否同步存储
 */
export const setStorage = async (
  key: string, 
  data: any, 
  sync: boolean = false
): Promise<void> => {
  try {
    if (sync) {
      Taro.setStorageSync(key, data)
    } else {
      await Taro.setStorage({ key, data })
    }
  } catch (error) {
    console.error('存储数据失败:', error)
    throw error
  }
}

/**
 * 获取本地存储（有默认值）
 * @param key 存储键
 * @param defaultValue 默认值
 * @param sync 是否同步获取
 */
export function getStorage<T,>(
  key: string,
  defaultValue: T,
  sync?: boolean
): Promise<T>

/**
 * 获取本地存储（无默认值）
 * @param key 存储键
 * @param sync 是否同步获取
 */
export function getStorage<T = any>(
  key: string,
  sync?: boolean
): Promise<T | null>

/**
 * 获取本地存储实现
 */
export async function getStorage<T = any>(
  key: string,
  defaultValueOrSync?: T | boolean,
  sync: boolean = false
): Promise<T | null> {
  // 参数解析
  let defaultValue: T | null = null
  let isSync = sync

  if (typeof defaultValueOrSync === 'boolean') {
    // 第二个参数是 sync
    isSync = defaultValueOrSync
  } else {
    // 第二个参数是 defaultValue
    defaultValue = defaultValueOrSync as T
  }

  try {
    if (isSync) {
      const result = Taro.getStorageSync(key)
      if (result !== undefined && result !== null) {
        return result
      }
      return defaultValue
    } else {
      const result = await Taro.getStorage({ key })
      if (result.data !== undefined && result.data !== null) {
        return result.data
      }
      return defaultValue
    }
  } catch (error) {
    console.warn('获取存储数据失败:', error)
    return defaultValue
  }
}

/**
 * 移除本地存储
 * @param key 存储键
 * @param sync 是否同步移除
 */
export const removeStorage = async (
  key: string, 
  sync: boolean = false
): Promise<void> => {
  try {
    if (sync) {
      Taro.removeStorageSync(key)
    } else {
      await Taro.removeStorage({ key })
    }
  } catch (error) {
    console.error('移除存储数据失败:', error)
    throw error
  }
}

/**
 * 清空本地存储
 * @param sync 是否同步清空
 */
export const clearStorage = async (sync: boolean = false): Promise<void> => {
  try {
    if (sync) {
      Taro.clearStorageSync()
    } else {
      await Taro.clearStorage()
    }
  } catch (error) {
    console.error('清空存储失败:', error)
    throw error
  }
}

// ==================== 格式化相关 ====================

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式化模板
 */
export const formatDate = (
  date: Date | string | number, 
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string => {
  const d = new Date(date)
  
  if (isNaN(d.getTime())) {
    return ''
  }

  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param decimals 小数位数
 */
export const formatFileSize = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']

  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * 格式化数字
 * @param num 数字
 * @param options 格式化选项
 */
export const formatNumber = (
  num: number, 
  options: {
    decimals?: number
    separator?: string
    prefix?: string
    suffix?: string
  } = {}
): string => {
  const { decimals = 0, separator = ',', prefix = '', suffix = '' } = options
  
  const parts = num.toFixed(decimals).split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator)
  
  return prefix + parts.join('.') + suffix
}

/**
 * 格式化手机号
 * @param phone 手机号
 * @param separator 分隔符
 */
export const formatPhone = (phone: string, separator: string = ' '): string => {
  const cleaned = phone.replace(/\D/g, '')
  const match = cleaned.match(/^(\d{3})(\d{4})(\d{4})$/)
  
  if (match) {
    return `${match[1]}${separator}${match[2]}${separator}${match[3]}`
  }
  
  return phone
}

// ==================== 验证相关 ====================

/**
 * 验证手机号
 * @param phone 手机号
 */
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证邮箱
 * @param email 邮箱
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证身份证号
 * @param idCard 身份证号
 */
export const validateIdCard = (idCard: string): boolean => {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return idCardRegex.test(idCard)
}

/**
 * 验证URL
 * @param url URL地址
 */
export const validateUrl = (url: string): boolean => {
  // 微信小程序环境下的URL验证
  const urlRegex = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/
  return urlRegex.test(url)
}

// ==================== 工具函数 ====================

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: number | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 限制时间
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深拷贝
 * @param obj 要拷贝的对象
 */
export const deepClone = <T,>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  
  return obj
}

/**
 * 生成唯一ID
 * @param prefix 前缀
 */
export const generateId = (prefix: string = 'id'): string => {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

/**
 * 延迟执行
 * @param ms 延迟毫秒数
 */
export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// ==================== 数组相关 ====================

/**
 * 数组去重
 * @param arr 数组
 * @param key 对象数组的去重键
 */
export const uniqueArray = <T,>(arr: T[], key?: keyof T): T[] => {
  if (!key) {
    return [...new Set(arr)]
  }

  const seen = new Set()
  return arr.filter(item => {
    const value = item[key]
    if (seen.has(value)) {
      return false
    }
    seen.add(value)
    return true
  })
}

/**
 * 数组分组
 * @param arr 数组
 * @param key 分组键
 */
export const groupBy = <T,>(arr: T[], key: keyof T): Record<string, T[]> => {
  return arr.reduce((groups, item) => {
    const group = String(item[key])
    if (!groups[group]) {
      groups[group] = []
    }
    groups[group].push(item)
    return groups
  }, {} as Record<string, T[]>)
}

/**
 * 数组排序
 * @param arr 数组
 * @param key 排序键
 * @param order 排序方向
 */
export const sortArray = <T,>(
  arr: T[],
  key: keyof T,
  order: 'asc' | 'desc' = 'asc'
): T[] => {
  return [...arr].sort((a, b) => {
    const aVal = a[key]
    const bVal = b[key]

    if (aVal < bVal) return order === 'asc' ? -1 : 1
    if (aVal > bVal) return order === 'asc' ? 1 : -1
    return 0
  })
}

/**
 * 数组分页
 * @param arr 数组
 * @param page 页码
 * @param pageSize 每页大小
 */
export const paginateArray = <T,>(
  arr: T[],
  page: number,
  pageSize: number
): {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
} => {
  const total = arr.length
  const totalPages = Math.ceil(total / pageSize)
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const data = arr.slice(start, end)

  return {
    data,
    total,
    page,
    pageSize,
    totalPages
  }
}

// ==================== 对象相关 ====================

/**
 * 对象深度合并
 * @param target 目标对象
 * @param sources 源对象
 */
export const deepMerge = <T extends Record<string, any>>(
  target: T,
  ...sources: Partial<T>[]
): T => {
  if (!sources.length) return target

  const source = sources.shift()
  if (!source) return target

  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      if (!target[key] || typeof target[key] !== 'object') {
        target[key] = {} as any
      }
      deepMerge(target[key], source[key])
    } else {
      target[key] = source[key] as any
    }
  }

  return deepMerge(target, ...sources)
}

/**
 * 获取对象深层属性
 * @param obj 对象
 * @param path 属性路径
 * @param defaultValue 默认值
 */
export const getDeepProperty = <T = any>(
  obj: any,
  path: string,
  defaultValue?: T
): T => {
  const keys = path.split('.')
  let result = obj

  for (const key of keys) {
    if (result === null || result === undefined || !(key in result)) {
      return defaultValue as T
    }
    result = result[key]
  }

  return result as T
}

/**
 * 设置对象深层属性
 * @param obj 对象
 * @param path 属性路径
 * @param value 值
 */
export const setDeepProperty = (obj: any, path: string, value: any): void => {
  const keys = path.split('.')
  const lastKey = keys.pop()!
  let current = obj

  for (const key of keys) {
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {}
    }
    current = current[key]
  }

  current[lastKey] = value
}

/**
 * 过滤对象属性
 * @param obj 对象
 * @param predicate 过滤函数
 */
export const filterObject = <T extends Record<string, any>>(
  obj: T,
  predicate: (key: string, value: any) => boolean
): Partial<T> => {
  const result: Partial<T> = {}

  for (const [key, value] of Object.entries(obj)) {
    if (predicate(key, value)) {
      result[key as keyof T] = value
    }
  }

  return result
}

// ==================== 字符串相关 ====================

/**
 * 首字母大写
 * @param str 字符串
 */
export const capitalize = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

/**
 * 驼峰转下划线
 * @param str 字符串
 */
export const camelToSnake = (str: string): string => {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
}

/**
 * 下划线转驼峰
 * @param str 字符串
 */
export const snakeToCamel = (str: string): string => {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * 截断字符串
 * @param str 字符串
 * @param length 长度
 * @param suffix 后缀
 */
export const truncate = (str: string, length: number, suffix: string = '...'): string => {
  if (str.length <= length) return str
  return str.slice(0, length) + suffix
}

/**
 * 移除HTML标签
 * @param str 字符串
 */
export const stripHtml = (str: string): string => {
  return str.replace(/<[^>]*>/g, '')
}

// ==================== 小程序相关 ====================

/**
 * 获取系统信息
 */
export const getSystemInfo = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    try {
      // 使用新的API组合获取系统信息
      const deviceInfo = Taro.getDeviceInfo()
      const windowInfo = Taro.getWindowInfo()
      const appBaseInfo = Taro.getAppBaseInfo()

      // 组合成类似原来getSystemInfo的格式
      const systemInfo = {
        ...deviceInfo,
        ...windowInfo,
        ...appBaseInfo
      }

      resolve(systemInfo)
    } catch (error) {
      reject(error)
    }
  })
}

/**
 * 显示Toast
 * @param title 标题
 * @param options 选项
 */
export const showToast = (
  title: string,
  options: {
    icon?: 'success' | 'error' | 'loading' | 'none'
    duration?: number
    mask?: boolean
  } = {}
): void => {
  const { icon = 'none', duration = 2000, mask = false } = options

  Taro.showToast({
    title,
    icon,
    duration,
    mask
  })
}

/**
 * 显示加载中
 * @param title 标题
 * @param mask 是否显示透明蒙层
 */
export const showLoading = (title: string = '加载中...', mask: boolean = true): void => {
  Taro.showLoading({
    title,
    mask
  })
}

/**
 * 隐藏加载中
 */
export const hideLoading = (): void => {
  Taro.hideLoading()
}

/**
 * 显示模态对话框
 * @param options 选项
 */
export const showModal = (options: {
  title?: string
  content: string
  showCancel?: boolean
  cancelText?: string
  confirmText?: string
}): Promise<any> => {
  const {
    title = '提示',
    content,
    showCancel = true,
    cancelText = '取消',
    confirmText = '确定'
  } = options

  return new Promise((resolve, reject) => {
    Taro.showModal({
      title,
      content,
      showCancel,
      cancelText,
      confirmText,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 选择图片
 * @param options 选项
 */
export const chooseImage = (options: {
  count?: number
  sizeType?: ('original' | 'compressed')[]
  sourceType?: ('album' | 'camera')[]
} = {}): Promise<any> => {
  const {
    count = 1,
    sizeType = ['original', 'compressed'],
    sourceType = ['album', 'camera']
  } = options

  return new Promise((resolve, reject) => {
    Taro.chooseImage({
      count,
      sizeType,
      sourceType,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 预览图片
 * @param urls 图片链接数组
 * @param current 当前显示图片链接
 */
export const previewImage = (urls: string[], current?: string): void => {
  Taro.previewImage({
    urls,
    current: current || urls[0]
  })
}

/**
 * 复制到剪贴板
 * @param data 要复制的内容
 * @param showToast 是否显示提示
 */
export const setClipboardData = async (
  data: string,
  showToast: boolean = true
): Promise<void> => {
  try {
    await Taro.setClipboardData({ data })
    if (showToast) {
      Taro.showToast({
        title: '复制成功',
        icon: 'success'
      })
    }
  } catch (error) {
    console.error('复制失败:', error)
    if (showToast) {
      Taro.showToast({
        title: '复制失败',
        icon: 'error'
      })
    }
    throw error
  }
}

// ==================== 微信小程序特有功能 ====================

/**
 * 获取用户信息
 * @param withCredentials 是否带上登录态信息
 */
export const getUserInfo = (withCredentials: boolean = false): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.getUserInfo({
      withCredentials,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 获取用户授权设置
 */
export const getSetting = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.getSetting({
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 打开设置页面
 */
export const openSetting = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.openSetting({
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 获取位置信息
 * @param type 位置类型
 */
export const getLocation = (type: 'wgs84' | 'gcj02' = 'wgs84'): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.getLocation({
      type,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 选择位置
 */
export const chooseLocation = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.chooseLocation({
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 打开位置
 * @param latitude 纬度
 * @param longitude 经度
 * @param name 位置名称
 * @param address 地址
 */
export const openLocation = (options: {
  latitude: number
  longitude: number
  name?: string
  address?: string
  scale?: number
}): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.openLocation({
      ...options,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 扫码
 * @param onlyFromCamera 是否只能从相机扫码
 * @param scanType 扫码类型
 */
export const scanCode = (options: {
  onlyFromCamera?: boolean
  scanType?: ('barCode' | 'qrCode' | 'datamatrix' | 'pdf417')[]
} = {}): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.scanCode({
      ...options,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 拨打电话
 * @param phoneNumber 电话号码
 */
export const makePhoneCall = (phoneNumber: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.makePhoneCall({
      phoneNumber,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 保存图片到相册
 * @param filePath 图片文件路径
 */
export const saveImageToPhotosAlbum = (filePath: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.saveImageToPhotosAlbum({
      filePath,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 获取网络类型
 */
export const getNetworkType = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.getNetworkType({
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 监听网络状态变化
 * @param callback 回调函数
 */
export const onNetworkStatusChange = (callback: (res: any) => void): void => {
  Taro.onNetworkStatusChange(callback)
}

/**
 * 获取设备信息
 */
export const getDeviceInfo = (): any => {
  try {
    return Taro.getDeviceInfo()
  } catch (error) {
    console.error('获取设备信息失败:', error)
    return {}
  }
}

/**
 * 获取窗口信息
 */
export const getWindowInfo = (): any => {
  try {
    return Taro.getWindowInfo()
  } catch (error) {
    console.error('获取窗口信息失败:', error)
    return {}
  }
}

/**
 * 振动反馈
 * @param type 振动类型
 */
export const vibrateShort = (type: 'heavy' | 'medium' | 'light' = 'medium'): void => {
  Taro.vibrateShort({ type })
}

/**
 * 长振动
 */
export const vibrateLong = (): void => {
  Taro.vibrateLong()
}

// ==================== 小程序页面相关 ====================

/**
 * 获取当前页面栈
 */
export const getCurrentPages = (): any[] => {
  return Taro.getCurrentPages()
}

/**
 * 获取当前页面路径
 */
export const getCurrentPagePath = (): string => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  return currentPage ? `/${currentPage.route}` : ''
}

/**
 * 获取当前页面参数
 */
export const getCurrentPageOptions = (): Record<string, any> => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  return currentPage ? currentPage.options || {} : {}
}

/**
 * 设置页面标题
 * @param title 页面标题
 */
export const setNavigationBarTitle = (title: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.setNavigationBarTitle({
      title,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 设置导航栏颜色
 * @param frontColor 前景颜色
 * @param backgroundColor 背景颜色
 */
export const setNavigationBarColor = (options: {
  frontColor: '#000000' | '#ffffff'
  backgroundColor: string
  animation?: {
    duration?: number
    timingFunc?: 'linear' | 'easeIn' | 'easeOut' | 'easeInOut'
  }
}): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.setNavigationBarColor({
      ...options,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 显示导航栏加载动画
 */
export const showNavigationBarLoading = (): void => {
  Taro.showNavigationBarLoading()
}

/**
 * 隐藏导航栏加载动画
 */
export const hideNavigationBarLoading = (): void => {
  Taro.hideNavigationBarLoading()
}

// ==================== 数据转换相关 ====================

/**
 * rpx 转 px
 * @param rpx rpx值
 */
export const rpxToPx = (rpx: number): number => {
  const windowInfo = Taro.getWindowInfo()
  return (rpx * windowInfo.windowWidth) / 750
}

/**
 * px 转 rpx
 * @param px px值
 */
export const pxToRpx = (px: number): number => {
  const windowInfo = Taro.getWindowInfo()
  return (px * 750) / windowInfo.windowWidth
}

/**
 * 获取图片信息
 * @param src 图片路径
 */
export const getImageInfo = (src: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.getImageInfo({
      src,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 下载文件
 * @param url 文件URL
 * @param filePath 保存路径
 */
export const downloadFile = (url: string, filePath?: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.downloadFile({
      url,
      filePath,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 上传文件
 * @param url 上传地址
 * @param filePath 文件路径
 * @param name 文件对应的 key
 * @param formData 额外的表单数据
 */
export const uploadFile = (options: {
  url: string
  filePath: string
  name: string
  formData?: Record<string, any>
  header?: Record<string, any>
}): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.uploadFile({
      ...options,
      success: resolve,
      fail: reject
    })
  })
}

// ==================== 本地文件相关 ====================

/**
 * 保存文件到本地
 * @param tempFilePath 临时文件路径
 */
export const saveFile = (tempFilePath: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.saveFile({
      tempFilePath,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 获取已保存的文件列表
 */
export const getSavedFileList = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.getSavedFileList({
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 获取本地文件信息
 * @param filePath 文件路径
 */
export const getFileInfo = (filePath: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.getFileInfo({
      filePath,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 删除本地文件
 * @param filePath 文件路径
 */
export const removeSavedFile = (filePath: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.removeSavedFile({
      filePath,
      success: resolve,
      fail: reject
    })
  })
}

// ==================== 音频相关 ====================

/**
 * 播放音频
 * @param filePath 音频文件路径
 */
export const playVoice = (filePath: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.playVoice({
      filePath,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 暂停播放音频
 */
export const pauseVoice = (): void => {
  Taro.pauseVoice()
}

/**
 * 停止播放音频
 */
export const stopVoice = (): void => {
  Taro.stopVoice()
}

// ==================== 实用工具函数 ====================

/**
 * 检查是否为开发环境
 */
export const isDev = (): boolean => {
  return process.env.NODE_ENV === 'development'
}

/**
 * 检查是否为生产环境
 */
export const isProd = (): boolean => {
  return process.env.NODE_ENV === 'production'
}

/**
 * 获取小程序版本信息
 */
export const getAppVersion = (): string => {
  try {
    const accountInfo = Taro.getAccountInfoSync()
    return accountInfo.miniProgram.version || '1.0.0'
  } catch (error) {
    console.error('获取版本信息失败:', error)
    return '1.0.0'
  }
}

/**
 * 检查小程序更新
 */
export const checkForUpdate = (): void => {
  if (Taro.canIUse('getUpdateManager')) {
    const updateManager = Taro.getUpdateManager()

    updateManager.onCheckForUpdate((res) => {
      console.log('检查更新结果:', res.hasUpdate)
    })

    updateManager.onUpdateReady(() => {
      Taro.showModal({
        title: '更新提示',
        content: '新版本已经准备好，是否重启应用？',
        success: (res) => {
          if (res.confirm) {
            updateManager.applyUpdate()
          }
        }
      })
    })

    updateManager.onUpdateFailed(() => {
      console.error('新版本下载失败')
    })
  }
}

/**
 * 节流函数（适用于小程序）
 * @param func 要节流的函数
 * @param limit 限制时间
 */
export const throttleForMiniProgram = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean = false

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

/**
 * 防抖函数（适用于小程序）
 * @param func 要防抖的函数
 * @param wait 等待时间
 */
export const debounceForMiniProgram = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: number | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

/**
 * 格式化时间显示
 * @param dateString 时间字符串
 */
export const formatTime = (dateString: string): string => {
  if (!dateString) return ''

  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }

  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000))
    return `${minutes}分钟前`
  }

  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return `${hours}小时前`
  }

  // 小于7天
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    return `${days}天前`
  }

  // 超过7天，显示具体日期
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  // 如果是今年，不显示年份
  if (year === now.getFullYear()) {
    return `${month}-${day}`
  }

  return `${year}-${month}-${day}`
}
