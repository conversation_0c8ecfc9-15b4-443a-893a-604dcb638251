/**
 * 文件上传接口
 */
import request from '../../index'

// 文件上传接口
export const uploadApi = {
  // 上传图片
  uploadImage: (filePath: string) => 
    request.upload<UploadAPI.UploadResponse>({
      url: '/upload/image',
      filePath,
      name: 'file'
    }),
  
  // 上传文件
  uploadFile: (filePath: string) => 
    request.upload<UploadAPI.UploadResponse>({
      url: '/upload/file',
      filePath,
      name: 'file'
    }),
}
