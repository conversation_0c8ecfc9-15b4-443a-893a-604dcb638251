/**
 * 文章模块类型声明
 * 基于 api.json 接口文档定义
 */

// 引用通用类型
/// <reference path="../../common-types.d.ts" />

declare namespace ArticleAPI {
  // 文章详情信息类型（基于 LawArticleDetailDto）
  interface ArticleDetailInfo extends CommonTypes.BaseEntity {
    title: string
    content: string  // HTML/富文本
    categoryId: number
    categoryName: string
    likeCount: number
    viewCount: number
    favoriteCount: number
    createdAt: string
  }

  // 文章列表信息类型（基于 LawArticleLstDto）
  interface ArticleListInfo extends CommonTypes.BaseEntity {
    title: string
    categoryId: number
    categoryName: string
    likeCount: number
    viewCount: number
    favoriteCount: number
    createdAt: string
  }

  // 律师文章统计类型
  interface LawyerArticleCountDto extends CommonTypes.LawyerArticleCountDto {}

  // 获取文章详情请求参数
  interface GetArticleDetailRequest {
    articleId: number
  }

  // 获取文章详情响应
  interface GetArticleDetailResponse {
    detail: ArticleDetailInfo
    lawyerInfo: LawyerAPI.LawyerInfo
    recommendList: ArticleListInfo[]
  }

  // 获取文章列表请求参数
  interface GetArticleListRequest extends CommonTypes.BaseSearchRequest {
    title?: string  // 文章标题模糊搜索
  }

  // 获取文章列表响应
  interface GetArticleListResponse extends CommonTypes.PaginationResponse<ArticleListInfo> {}

  // 获取律师文章列表请求参数
  interface GetLawyerArticleListRequest extends CommonTypes.BaseSearchRequest {
    lawyerId: number
    title?: string  // 文章标题模糊搜索
  }

  // 获取律师文章列表响应
  interface GetLawyerArticleListResponse extends CommonTypes.PaginationResponse<ArticleListInfo> {
    ArticleCount: LawyerArticleCountDto[]
  }

  // 使用通用类型别名
  type PaginationResponse<T> = CommonTypes.PaginationResponse<T>
  type ListResponse<T> = CommonTypes.ListResponse<T>
}
