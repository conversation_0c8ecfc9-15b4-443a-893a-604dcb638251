/**
 * AreaSelect 省市区选择组件
 * 
 * 基于Taro Picker组件的region模式封装
 * 使用内置的省市区数据，无需手动加载接口
 */
import React, { useState, useEffect } from 'react'
import { View } from '@tarojs/components'
import { Picker } from '@tarojs/components'
import { AreaSelectProps, AreaSelectResult } from './types'
import './index.scss'

const AreaSelect: React.FC<AreaSelectProps> = ({
  children,
  onChange,
  value,
  disabled = false,
  className = '',
  placeholder = '请选择省市区'
}) => {
  // 当前选择的值 [省份, 城市, 区县]
  const [selectedValue, setSelectedValue] = useState<string[]>(['', '', ''])

  // 初始化组件时设置默认值
  useEffect(() => {
    if (value) {
      // value 格式: "省份名/城市名/区县名" 或 "省份名/城市名"
      const areas = value.split('/')
      if (areas.length >= 2) {
        setSelectedValue([areas[0] || '', areas[1] || '', areas[2] || ''])
      }
    }
  }, [value])

  // 处理选择变化
  const handlePickerChange = (e: any) => {
    const { value: selectedAreas } = e.detail
    const [province, city, district] = selectedAreas
    
    // 更新选择的值
    setSelectedValue(selectedAreas)

    // 构建结果
    const result: AreaSelectResult = {
      areaString: district 
        ? `${province}/${city}/${district}`
        : `${province}/${city}`,
      areaIds: '', // region模式无法获取具体ID，如需ID请使用API接口
      province: { id: '', name: province },
      city: { id: '', name: city },
      district: district ? { id: '', name: district } : undefined
    }

    // 触发回调
    onChange?.(result)
  }

  return (
    <View className={`area-select ${className} ${disabled ? 'area-select--disabled' : ''}`}>
      <Picker
        mode="region"
        value={selectedValue}
        onChange={handlePickerChange}
        disabled={disabled}
      >
        <View className="area-select__trigger">
          {children}
        </View>
      </Picker>
    </View>
  )
}

// 导出主组件
export default AreaSelect

// 导出类型定义
export type {
  AreaSelectProps,
  AreaSelectResult,
  AreaItem
} from './types'

// 导出常量
export { MUNICIPALITIES } from './types'
