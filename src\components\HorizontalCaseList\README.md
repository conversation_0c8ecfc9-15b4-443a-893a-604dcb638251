# HorizontalCaseList 横向案例组件

## 功能特性

- 🎯 **横向滑动**：支持横向滑动查看案例列表
- 📱 **响应式设计**：适配不同屏幕尺寸
- 🔄 **数据加载**：支持API数据获取和Mock数据fallback
- 🎨 **卡片设计**：520rpx×243rpx卡片，#FAE8CD背景色
- 📝 **内容展示**：标题3行省略、分类标签、时间信息
- 🔗 **点击跳转**：点击卡片跳转到案例详情页
- 🎛️ **筛选支持**：支持外部传入筛选条件

## 使用方法

### 基础用法

```tsx
import HorizontalCaseList from '@/components/HorizontalCaseList'

// 基础使用
<HorizontalCaseList />
```

### 带筛选条件

```tsx
import HorizontalCaseList from '@/components/HorizontalCaseList'

const filterParams = {
  categoryId: 1,
  status: 2
}

<HorizontalCaseList 
  filterParams={filterParams}
  className="custom-case-list"
/>
```

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| filterParams | `CaseAPI.GetCaseListRequest` | `{}` | 案例列表筛选条件 |
| className | `string` | `''` | 自定义样式类名 |

## 样式规格

### 组件容器
- **宽度**：100%
- **布局**：横向滚动容器

### 案例卡片
- **尺寸**：520rpx × 243rpx
- **背景**：#FAE8CD
- **圆角**：14rpx
- **内边距**：30rpx
- **间距**：卡片间距30rpx
- **阴影**：0rpx 2rpx 48rpx 0rpx rgba(0, 0, 0, 0.04)

### 标题样式
- **字重**：600
- **字号**：28rpx
- **颜色**：#000000
- **行高**：36rpx
- **显示**：最多3行，超出省略

### 分类标签
- **字重**：600
- **字号**：20rpx
- **颜色**：#BD8A4F
- **行高**：25rpx

### 时间信息
- **字重**：600
- **字号**：20rpx
- **颜色**：#000000
- **行高**：25rpx
- **图标**：25rpx × 25rpx 时钟图标

## 数据结构

### CaseCardInfo 类型
```typescript
interface CaseCardInfo {
  id: string
  title: string
  categoryId: number
  categoryName: string
  status: number
  rejectReason: string
  viewCount: number
  creator: string
  reviewer: string
  reviewTime: string
  createTime: string
}
```

### 筛选参数类型
```typescript
interface HorizontalCaseListProps {
  filterParams?: CaseAPI.GetCaseListRequest
  className?: string
}
```

## 功能说明

### 数据获取
- 调用 `caseApi.getCaseList()` 获取案例数据
- 最多显示5个案例（pageSize: 5）
- API失败时使用Mock数据作为fallback

### 横向滑动
- 使用 Taro ScrollView 组件
- 设置 `scrollX={true}` 启用横向滚动
- 隐藏滚动条 `showScrollbar={false}`
- 增强模式 `enhanced={true}`

### 点击跳转
- 点击卡片跳转到案例详情页
- 路径：`/subpackages/detail/case/index?id=${caseInfo.id}`
- 使用统一的 `navigateToPage` 方法

### 状态管理
- **加载状态**：显示"加载中..."
- **空状态**：显示"暂无案例信息"
- **数据状态**：显示案例卡片列表

## 交互效果

### 卡片点击
- 点击时缩放到 0.98
- 0.2s 缓动过渡效果

### 滚动体验
- 横向滑动流畅
- 支持惯性滚动
- 边界回弹效果

## 布局结构

```
┌─────────────────────────────────────────────────────────┐
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────┐ │
│ │         │ │         │ │         │ │         │ │     │ │
│ │ 案例1   │ │ 案例2   │ │ 案例3   │ │ 案例4   │ │ ... │ │
│ │         │ │         │ │         │ │         │ │     │ │
│ │ 分类 时间│ │ 分类 时间│ │ 分类 时间│ │ 分类 时间│ │     │ │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘ └─────┘ │
└─────────────────────────────────────────────────────────┘
```

## 使用场景

- **首页推荐**：展示热门案例
- **分类页面**：展示特定分类的案例
- **搜索结果**：展示搜索匹配的案例
- **律师主页**：展示律师相关案例

## 自定义样式示例

```scss
// 自定义案例列表样式
.custom-case-list {
  // 修改背景色
  .case-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    
    .case-card__title {
      color: #fff;
    }
    
    .case-card__category {
      color: #fff;
    }
  }
}
```

## 性能优化

### 1. 数据限制
- 最多显示5个案例，避免过多DOM节点
- 使用 `flex-shrink: 0` 避免卡片压缩

### 2. 滚动优化
- 使用 `enhanced={true}` 启用增强模式
- 设置 `bounces={false}` 减少不必要的动画

### 3. 图片优化
- 时钟图标使用 `mode="aspectFit"`
- 支持图片懒加载

## 注意事项

1. **数据格式**：确保API返回的数据格式与 CaseCardInfo 类型匹配
2. **路由配置**：确保案例详情页路由已正确配置
3. **权限控制**：根据需要添加案例访问权限检查
4. **错误处理**：API调用失败时会自动使用Mock数据
5. **性能考虑**：大量案例时考虑虚拟滚动优化

## 扩展功能

### 1. 添加更多操作
```tsx
// 添加收藏功能
const handleFavorite = (caseInfo: CaseCardInfo) => {
  // 收藏逻辑
}

// 添加分享功能
const handleShare = (caseInfo: CaseCardInfo) => {
  // 分享逻辑
}
```

### 2. 支持无限滚动
```tsx
// 添加加载更多功能
const loadMore = () => {
  // 加载更多案例
}
```

### 3. 添加筛选器
```tsx
// 支持快速筛选
const quickFilters = ['全部', '劳动纠纷', '房产纠纷', '交通事故']
```
