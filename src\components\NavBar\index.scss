/**
 * 导航栏组件样式
 */

.nav-bar {
  width: 100%;
  top: 0;
  left: 0;
  z-index: 999;
  background-color: #ffffff;

  // 固定定位
  &--fixed {
    position: fixed;
  }

  // 底部边框
  &--border {
    border-bottom: 1rpx solid #e5e5e5;
  }

  // 内容区域
  &__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    box-sizing: border-box;
    padding-left: 32rpx;
    padding-right: 32rpx;
  }

  // 左侧区域
  &__left {
    display: flex;
    align-items: center;
    min-width: 120rpx;
    justify-content: flex-start;
  }

  // 中间区域
  &__center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    margin: 0 32rpx;
  }

  // 右侧区域
  &__right {
    display: flex;
    align-items: center;
    min-width: 120rpx;
    justify-content: flex-end;
  }

  // 返回按钮
  &__back {
    display: flex;
    align-items: center;
    padding: 8rpx 16rpx;
    border-radius: 8rpx;
    transition: background-color 0.2s;

    &:active {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }

  // 返回按钮图标
  &__back-icon {
    width: 24rpx;
    height: 24rpx;
    margin-right: 8rpx;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='15,18 9,12 15,6'%3E%3C/polyline%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  // 返回按钮文字
  &__back-text {
    font-size: 28rpx;
    color: #333333;
  }

  // 标题
  &__title {
    text-align: center;
    max-width: 400rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 328rpx;
    height: 50rpx;
    font-weight: 800;
    font-size: 36rpx;
    color: #BD8A4F;
    line-height: 50rpx;
    text-align: center;
    font-style: normal;
  }

  // Logo
  &__logo {
    width: 137rpx;
    height: 43rpx;
    display: block;
  }
}

// 占位符（当导航栏固定定位时，为页面内容预留空间）
.nav-bar-placeholder {
  width: 100%;
  height: var(--nav-bar-height, 88rpx);
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .nav-bar {
    background-color: #1a1a1a;
    border-bottom-color: #333333;

    &__back-text,
    &__title {
      color: #ffffff;
    }

    &__back-icon {
      filter: invert(1);
    }
  }
}