/**
 * 律师文章数据统计组件
 * 
 * 基于 ContentNumDto 类型设计，展示律师的文章统计数据
 */
import React from 'react'
import { View, Text } from '@tarojs/components'
import './index.scss'

// 组件属性
export interface LawyerArticleStatsProps {
  // 内容统计数据
  contentNum: LawyerAPI.ContentNumDto | null
  // 自定义标题
  title?: string
  // 自定义样式类名
  className?: string
  // 是否显示加载状态
  loading?: boolean
}

const LawyerArticleStats: React.FC<LawyerArticleStatsProps> = ({
  contentNum,
  title = '文章数据',
  className = '',
  loading = false
}) => {
  // 格式化数字显示
  const formatNumber = (num: number): string => {
    if (num >= 10000) {
      return `${(num / 10000).toFixed(1)}万`
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`
    }
    return num.toString()
  }

  return (
    <View className={`lawyer-article-stats ${className}`}>
      <Text className='lawyer-article-stats__title'>{title}</Text>
      
      {loading ? (
        <View className='lawyer-article-stats__loading'>
          <View className='lawyer-article-stats__loading-item' />
          <View className='lawyer-article-stats__loading-item' />
          <View className='lawyer-article-stats__loading-item' />
        </View>
      ) : (
        <View className='lawyer-article-stats__list'>
          <View className='lawyer-article-stats__item'>
            <View className='lawyer-article-stats__number'>
              <Text className='lawyer-article-stats__number-text'>
                {formatNumber(contentNum?.articleNum || 0)}
              </Text>
              <Text className='lawyer-article-stats__number-unit'>篇</Text>
            </View>
            <Text className='lawyer-article-stats__label'>发表文章</Text>
          </View>
          <View className='lawyer-article-stats__item'>
            <View className='lawyer-article-stats__number'>
              <Text className='lawyer-article-stats__number-text'>
                {formatNumber(contentNum?.caseNum || 0)}
              </Text>
              <Text className='lawyer-article-stats__number-unit'>件</Text>
            </View>
            <Text className='lawyer-article-stats__label'>处理案例</Text>
          </View>
          <View className='lawyer-article-stats__item'>
            <View className='lawyer-article-stats__number'>
              <Text className='lawyer-article-stats__number-text'>
                {formatNumber(contentNum?.dynamicsNum || 0)}
              </Text>
              <Text className='lawyer-article-stats__number-unit'>条</Text>
            </View>
            <Text className='lawyer-article-stats__label'>律师动态</Text>
          </View>
        </View>
      )}
    </View>
  )
}

export default LawyerArticleStats
