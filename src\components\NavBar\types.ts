/**
 * NavBar 组件类型定义
 */
import { ReactNode } from 'react'

// 导航栏属性接口
export interface NavBarProps {
  // 标题
  title?: string
  // 是否显示返回按钮
  showBack?: boolean
  // 返回按钮点击事件
  onBack?: () => void
  // 背景色
  backgroundColor?: string
  // 文字颜色
  textColor?: string
  // 是否显示底部边框
  showBorder?: boolean
  // 左侧插槽内容
  leftSlot?: ReactNode
  // 右侧插槽内容
  rightSlot?: ReactNode
  // 中间插槽内容（会覆盖title）
  centerSlot?: ReactNode
  // 是否显示logo
  showLogo?: boolean
  // logo图片路径
  logoSrc?: string
  // 是否固定定位
  fixed?: boolean
  // 自定义样式类名
  className?: string
}

// 导航栏主题类型
export type NavBarTheme = 'light' | 'dark' | 'transparent'

// 导航栏配置接口
export interface NavBarConfig {
  // 默认标题
  defaultTitle?: string
  // 默认背景色
  defaultBackgroundColor?: string
  // 默认文字颜色
  defaultTextColor?: string
  // 是否默认显示返回按钮
  defaultShowBack?: boolean
  // 是否默认显示边框
  defaultShowBorder?: boolean
  // 是否默认固定定位
  defaultFixed?: boolean
}

// 导航栏样式接口
export interface NavBarStyle {
  height?: string | number
  backgroundColor?: string
  color?: string
  borderBottom?: string
  zIndex?: number
}

// 导航栏按钮配置
export interface NavBarButton {
  text?: string
  icon?: string
  onClick?: () => void
  disabled?: boolean
  loading?: boolean
}

// 导航栏操作类型
export type NavBarAction = 'back' | 'close' | 'save' | 'edit' | 'share' | 'more'
