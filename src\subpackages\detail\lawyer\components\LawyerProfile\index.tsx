/**
 * 律师简介组件
 * 
 * 展示律师个人简介，支持展开/收起功能
 */
import React from 'react'
import { View, Text, Image } from '@tarojs/components'
// 导入展开更多图标
import clickMoreIcon from '@/assets/images/common-icon/click_more.png'
import './index.scss'

// 组件属性
export interface LawyerProfileProps {
  // 个人简介内容
  personalProfile: string
  // 是否展开
  isExpanded: boolean
  // 展开/收起切换回调
  onToggle: () => void
  // 自定义标题
  title?: string
  // 自定义展开阈值（字符数）
  expandThreshold?: number
  // 自定义样式类名
  className?: string
}

const LawyerProfile: React.FC<LawyerProfileProps> = ({
  personalProfile,
  isExpanded,
  onToggle,
  title = '个人简介',
  expandThreshold = 100,
  className = ''
}) => {
  // 是否需要显示展开/收起按钮
  const shouldShowToggle = personalProfile && personalProfile.length > expandThreshold

  return (
    <View className={`lawyer-profile ${className}`}>
      <Text className='lawyer-profile__title'>{title}</Text>
      <View className={`lawyer-profile__content ${isExpanded ? 'lawyer-profile__content--expanded' : ''}`}>
        <Text className='lawyer-profile__text'>{personalProfile}</Text>
      </View>
      {shouldShowToggle && (
        <View className='lawyer-profile__toggle' onClick={onToggle}>
          <Image
            className='lawyer-profile__icon'
            src={clickMoreIcon}
            mode='aspectFit'
          />
          <Text className='lawyer-profile__toggle-text'>
            {isExpanded ? '收起' : '展开更多'}
          </Text>
        </View>
      )}
    </View>
  )
}

export default LawyerProfile
