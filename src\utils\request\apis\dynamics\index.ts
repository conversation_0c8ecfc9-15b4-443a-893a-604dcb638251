/**
 * 律师动态相关接口
 * 基于 api.json 接口文档定义
 */
import request from '../../index'
import { BaseResponse } from '../../types'

// 律师动态相关接口
export const dynamicsApi = {
  // 获取动态列表（基于 /mini/find-law-dynamics/list）
  getDynamicsList: (params?: DynamicsAPI.GetDynamicsListRequest): Promise<BaseResponse<DynamicsAPI.GetDynamicsListResponse>> =>
    request.get<DynamicsAPI.GetDynamicsListResponse>('/mini/find-law-dynamics/list', params),

  // 获取动态详情（基于 /mini/find-law-dynamics/:dynamicsId/detail）
  getDynamicsDetail: (dynamicsId: number): Promise<BaseResponse<DynamicsAPI.GetDynamicsDetailResponse>> =>
    request.get<DynamicsAPI.GetDynamicsDetailResponse>(`/mini/find-law-dynamics/${dynamicsId}/detail`),

  // 获取律师动态列表（基于 /mini/find-law-dynamics/:lawyerId/list）
  getLawyerDynamicsList: (lawyerId: number, params?: DynamicsAPI.GetLawyerDynamicsListRequest): Promise<BaseResponse<DynamicsAPI.GetLawyerDynamicsListResponse>> =>
    request.get<DynamicsAPI.GetLawyerDynamicsListResponse>(`/mini/find-law-dynamics/${lawyerId}/list`, params),

  // 获取推荐动态列表（基于 /mini/find-law-dynamics/:dynamicsId/recommend-list）
  getRecommendDynamicsList: (dynamicsId: number, params?: DynamicsAPI.GetRecommendDynamicsListRequest): Promise<BaseResponse<DynamicsAPI.GetRecommendDynamicsListResponse>> =>
    request.get<DynamicsAPI.GetRecommendDynamicsListResponse>(`/mini/find-law-dynamics/${dynamicsId}/recommend-list`, params),

  // 获取动态分类列表（基于 /mini/common/law-dynamics/category-list）
  getDynamicsCategoryList: (): Promise<BaseResponse<DynamicsAPI.GetDynamicsCategoryListResponse>> =>
    request.get<DynamicsAPI.GetDynamicsCategoryListResponse>('/mini/common/law-dynamics/category-list'),

  // 获取律师个人中心动态列表（基于 /mini/owner-center-lawyer/law-dynamics/list）
  getOwnerCenterDynamicsList: (params?: DynamicsAPI.GetOwnerCenterDynamicsListRequest): Promise<BaseResponse<DynamicsAPI.GetOwnerCenterDynamicsListResponse>> =>
    request.get<DynamicsAPI.GetOwnerCenterDynamicsListResponse>('/mini/owner-center-lawyer/law-dynamics/list', params),

  // 文章收藏（基于 /mini/auth-law-article/:articleId/collect）
  collectArticle: (articleId: number): Promise<BaseResponse<DynamicsAPI.CollectArticleResponse>> =>
    request.post<DynamicsAPI.CollectArticleResponse>(`/mini/auth-law-article/${articleId}/collect`, { articleId }),

  // 文章点赞（基于 /mini/auth-law-article/:articleId/nice）
  likeArticle: (articleId: number): Promise<BaseResponse<DynamicsAPI.LikeArticleResponse>> =>
    request.post<DynamicsAPI.LikeArticleResponse>(`/mini/auth-law-article/${articleId}/nice`, { articleId }),
}
