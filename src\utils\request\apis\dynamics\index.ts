/**
 * 律师动态相关接口
 * 基于 api.json 接口文档定义
 */
import request from '../../index'

// 律师动态相关接口
export const dynamicsApi = {
  // 获取律师动态列表
  getDynamicsList: (params?: DynamicsAPI.GetDynamicsListRequest) => 
    request.get<DynamicsAPI.GetDynamicsListResponse>('/mini/find-law-dynamics/list', params),
  
  // 获取律师动态详情
  getDynamicsDetail: (dynamicsId: number) => 
    request.get<DynamicsAPI.GetDynamicsDetailResponse>(`/mini/find-law-dynamics/${dynamicsId}/detail`),

  // 获取律师动态列表
  getLawyerDynamicsList: (lawyerId: number, params?: DynamicsAPI.GetLawyerDynamicsListRequest) => 
    request.get<DynamicsAPI.GetLawyerDynamicsListResponse>(`/mini/find-law-dynamics/${lawyerId}/list`, params),

  // 获取动态分类列表
  getDynamicsCategoryList: () =>
    request.get<DynamicsAPI.GetDynamicsCategoryListResponse>('/mini/law-dynamics/category-list'),

  // 创建动态
  createDynamics: (params: DynamicsAPI.CreateDynamicsRequest) =>
    request.post<DynamicsAPI.CreateDynamicsResponse>('/mini/law-dynamics/create', params),

  // 更新动态
  updateDynamics: (params: DynamicsAPI.UpdateDynamicsRequest) =>
    request.put<DynamicsAPI.UpdateDynamicsResponse>(`/mini/law-dynamics/${params.id}/update`, params),
}
