# Service 组件全局使用指南

## 在 app.tsx 中全局使用

如果希望在整个应用中都显示客服组件，可以在 `src/app.tsx` 中添加：

```tsx
import React, { PropsWithChildren } from 'react'
import { useLaunch } from '@tarojs/taro'
import { Service } from '@/components'
import './app.scss'

function App({ children }: PropsWithChildren<any>) {
  useLaunch(() => {
    console.log('App launched.')
  })

  return (
    <>
      {children}
      {/* 全局客服组件 */}
      <Service />
    </>
  )
}

export default App
```

## 在特定页面中使用

如果只希望在特定页面显示客服组件：

```tsx
import React from 'react'
import { View } from '@tarojs/components'
import { Service } from '@/components'

const HomePage: React.FC = () => {
  return (
    <View className="home-page">
      {/* 页面内容 */}
      
      {/* 客服组件 */}
      <Service />
    </View>
  )
}

export default HomePage
```

## 条件显示客服

根据用户登录状态或页面类型控制显示：

```tsx
import React from 'react'
import { View } from '@tarojs/components'
import { Service } from '@/components'
import { useUserStore } from '@/stores/user'

const MyPage: React.FC = () => {
  const { isLoggedIn } = useUserStore()

  return (
    <View className="my-page">
      {/* 页面内容 */}
      
      {/* 只有登录用户才显示客服 */}
      <Service visible={isLoggedIn} />
    </View>
  )
}

export default MyPage
```

## 配置客服信息

在使用前，需要在组件中配置实际的客服信息：

### 1. 修改微信客服配置

编辑 `src/components/Service/index.tsx`：

```tsx
// 修改客服配置
const serviceConfig = {
  // 替换为实际的微信客服链接
  serviceUrl: 'https://work.weixin.qq.com/kfid/your_actual_kf_id',
  // 替换为实际的企业微信ID
  corpId: 'your_actual_corp_id',
  // 替换为实际的客服电话
  phoneNumber: '************'
}

// 在 handleServiceClick 函数中使用
const handleServiceClick = () => {
  Taro.openCustomerServiceChat({
    extInfo: {
      url: serviceConfig.serviceUrl
    },
    corpId: serviceConfig.corpId,
    // ... 其他配置
  })
}
```

### 2. 微信小程序后台配置

1. 登录微信小程序后台
2. 进入"客服功能"设置
3. 配置客服人员和自动回复
4. 获取客服链接和企业ID

## 样式自定义

### 修改组件样式

```scss
// 在你的页面样式文件中
.custom-service {
  // 修改背景色
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  // 修改位置
  right: 40rpx;
  bottom: 100rpx;
  
  // 修改大小
  width: 100rpx;
  height: 100rpx;
  
  .service-widget__text {
    color: #fff;
  }
}
```

```tsx
// 使用自定义样式
<Service className="custom-service" />
```

### 主题适配

```scss
// 深色主题适配
@media (prefers-color-scheme: dark) {
  .service-widget {
    background: #2c2c2c;
    box-shadow: 0 4rpx 20rpx rgba(255, 255, 255, 0.1);
    
    .service-widget__text {
      color: #fff;
    }
  }
}
```

## 最佳实践

### 1. 性能优化
- 使用条件渲染避免不必要的组件加载
- 在不需要客服的页面隐藏组件

### 2. 用户体验
- 在客服不可用时提供明确的提示
- 提供多种联系方式作为备用方案

### 3. 数据统计
```tsx
const handleServiceClick = () => {
  // 添加埋点统计
  analytics.track('customer_service_clicked', {
    page: getCurrentPagePath(),
    timestamp: Date.now()
  })
  
  // 原有的客服逻辑
  // ...
}
```

### 4. 错误处理
```tsx
const handleServiceClick = async () => {
  try {
    await Taro.openCustomerServiceChat({
      // 配置信息
    })
  } catch (error) {
    // 记录错误日志
    console.error('客服功能异常:', error)
    
    // 上报错误
    errorReporting.report(error)
    
    // 显示备用方案
    showFallbackOptions()
  }
}
```

## 注意事项

1. **权限配置**：确保小程序后台已开启客服功能
2. **平台兼容**：不同平台的客服实现方式可能不同
3. **网络环境**：考虑网络异常情况的处理
4. **用户隐私**：遵守相关隐私保护规定
5. **客服时间**：可以根据客服工作时间控制组件显示
