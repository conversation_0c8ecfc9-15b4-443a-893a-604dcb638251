/**
 * 律师文章列表组件样式
 */

.lawyer-article-list {
  background-color: #ffffff;
  border-radius: 0 0 20rpx 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

  &__scroll {
    max-height: 75vh; // 限制最大高度，支持滚动
  }

  &__item {
    padding: 30rpx;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:active {
      background-color: #F5F5F5;
    }
  }

  // 文章标题
  &__title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    line-height: 44rpx;
    margin-bottom: 20rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; // 两行省略
    overflow: hidden;
    text-overflow: ellipsis;
  }

  // 文章内容预览
  &__content {
    font-size: 28rpx;
    color: #666666;
    line-height: 40rpx;
    margin-bottom: 20rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3; // 三行省略
    overflow: hidden;
    text-overflow: ellipsis;
  }

  // 文章信息区域
  &__info {
    display: flex;
    align-items: center;
    gap: 30rpx;
  }

  &__info-item {
    display: flex;
    align-items: center;
    gap: 8rpx;
  }

  &__info-icon {
    width: 24rpx;
    height: 24rpx;
    flex-shrink: 0;
  }

  &__info-text {
    font-size: 24rpx;
    color: #999999;
    line-height: 28rpx;
  }

  // 分割线
  &__divider {
    height: 1rpx;
    background-color: #E0DEE5;
    margin-top: 20rpx;
  }

  // 加载状态
  &__loading {
    padding: 40rpx;
    text-align: center;
  }

  &__loading-text {
    font-size: 28rpx;
    color: #999999;
  }

  // 没有更多数据
  &__no-more {
    padding: 40rpx;
    text-align: center;
  }

  &__no-more-text {
    font-size: 28rpx;
    color: #999999;
  }

  // 空状态
  &__empty {
    padding: 80rpx 40rpx;
    text-align: center;
  }

  &__empty-text {
    font-size: 28rpx;
    color: #999999;
  }
}