/**
 * 案例模块类型声明
 * 基于 api.json 接口文档定义
 */

// 引用通用类型
/// <reference path="../../common-types.d.ts" />

declare namespace CaseAPI {
  // 案例详情信息类型（基于 FindLawCaseDetailDto）
  interface CaseDetailInfo extends CommonTypes.BaseEntity {
    title: string
    categoryId: number
    categoryName: string
    content: string  // HTML/富文本
    viewCount: number
    creator: string
    creatorId: number
    createdAt: string
  }

  // 案例列表信息类型（基于 FindLawCaseListDto）
  interface CaseListInfo extends CommonTypes.BaseEntity {
    title: string
    categoryId: number
    categoryName: string
    viewCount: number
    creator: string
    createdAt: string
  }

  // 案例分类类型（基于 LawCaseCategoryDto）
  interface CaseCategoryInfo extends CommonTypes.CategoryDto {}

  // 律师案例分类统计类型
  interface LawyerCaseCategoryCountDto extends CommonTypes.CategoryCountDto {}

  // 获取案例详情请求参数
  interface GetCaseDetailRequest {
    caseId: number
  }

  // 获取案例详情响应（基于 FindCaseDetailRes）
  interface GetCaseDetailResponse {
    detail: CaseDetailInfo
    lawyerInfo: LawyerAPI.LawyerInfo
  }

  // 获取案例列表请求参数
  interface GetCaseListRequest extends CommonTypes.BaseSearchRequest {
    title?: string  // 案例标题模糊搜索
    categoryId?: number  // 分类ID
  }

  // 获取案例列表响应
  interface GetCaseListResponse extends CommonTypes.PaginationResponse<CaseListInfo> {
    lawyerCaseCategoryNum: LawyerCaseCategoryCountDto
  }

  // 获取律师案例列表请求参数
  interface GetLawyerCaseListRequest extends CommonTypes.BaseSearchRequest {
    lawyerId: number
    title?: string  // 案例标题模糊搜索
    categoryId?: number  // 分类ID
  }

  // 获取律师案例列表响应
  interface GetLawyerCaseListResponse extends CommonTypes.PaginationResponse<CaseListInfo> {
    recommendList: CaseListInfo[]
    lawyerCaseCategoryNum: LawyerCaseCategoryCountDto
  }

  // 获取案例分类列表响应（基于 CaseCategoryListRes）
  interface GetCaseCategoryListResponse {
    list: CaseCategoryInfo[]
  }

  // 获取推荐案例列表请求参数（基于 RecommendCaseListReq）
  interface GetRecommendCaseListRequest extends CommonTypes.BaseSearchRequest {
    caseId: number
  }

  // 获取推荐案例列表响应（基于 RecommendCaseListRes）
  interface GetRecommendCaseListResponse extends CommonTypes.PaginationResponse<CaseListInfo> {}

  // 律师个人中心案例列表信息类型（基于 OwnerCenterLawCaseListDto）
  interface OwnerCenterCaseListInfo extends CommonTypes.BaseEntity {
    title: string
    categoryId: number
    categoryName: string
    viewCount: number
    creator: string
    createdAt: string
    status: number  // 状态：1待审核，2已发布，3已拒绝
  }

  // 获取律师个人中心案例列表请求参数（基于 OwnerCaseListReq）
  interface GetOwnerCenterCaseListRequest extends CommonTypes.BaseSearchRequest {
    title?: string  // 案例标题模糊搜索
    categoryId?: number  // 分类ID
  }

  // 获取律师个人中心案例列表响应（基于 OwnerCaseListRes）
  interface GetOwnerCenterCaseListResponse extends CommonTypes.PaginationResponse<OwnerCenterCaseListInfo> {
    lawyerCaseCategoryNum?: LawyerCaseCategoryCountDto
  }

  // 案件阶段类型（基于 CaseStageDto）
  interface CaseStageInfo extends CommonTypes.BaseEntity {
    name: string
  }

  // 获取案件阶段列表响应（基于 CaseStageListRes）
  interface GetCaseStageListResponse {
    list: CaseStageInfo[]
  }

  // 创建案例请求参数
  interface CreateCaseRequest {
    title: string
    categoryId: number
    content: string
  }

  // 创建案例响应
  interface CreateCaseResponse {
    id: number
    message: string
  }

  // 更新案例请求参数
  interface UpdateCaseRequest {
    id: number
    title: string
    categoryId: number
    content: string
  }

  // 更新案例响应
  interface UpdateCaseResponse {
    message: string
  }

  // 使用通用类型别名
  type PaginationResponse<T> = CommonTypes.PaginationResponse<T>
  type ListResponse<T> = CommonTypes.ListResponse<T>
}
