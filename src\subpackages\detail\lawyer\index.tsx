/**
 * 律师详情页面
 */
import React, { useState, useEffect } from 'react'
import { View, Text, Image } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import { navigateBack } from '@/utils'
import { lawyer<PERSON><PERSON> } from '@/utils/request/apis/lawyer'
import HorizontalDynamicsList from '@/components/HorizontalDynamicsList'

import PageLayout, { PageContent } from '@/components/PageLayout'
// 导入左侧按钮图标
import leftButtonIcon from '@/assets/images/common-icon/left_button.png'

// 导入浮动按键图标
import headsetOutlineIcon from '@/assets/images/common-icon/headset_outline.png'
import editIcon from '@/assets/images/common-icon/edit.png'
import lawyerBg from '@/assets/images/lawyer_bg.png'
import mockLawyer from '@/assets/images/mock_lawyer.png'
import CommentIcon from '@/assets/images/common-icon/comment.png'

import './index.scss'
import {
  LawyerAvatar,
  LawyerTabs,
  LawyerInfoTab,
  LawyerDynamicsTab,
  FloatingMenu,
  LawyerArticleTab,
  type TabType
} from './components'

const LawyerDetail: React.FC = () => {
  const router = useRouter()
  const { userId } = router.params

  const [lawyerInfo, setLawyerInfo] = useState<LawyerAPI.LawyerInfo | null>(null)
  const [lawyerCountData, setLawyerCountData] = useState<LawyerAPI.LawyerCountDataDto | null>(null)
  const [contentNum, setContentNum] = useState<LawyerAPI.ContentNumDto | null>(null)
  const [loading, setLoading] = useState(true)
  const [isFollowed, setIsFollowed] = useState(false)
  const [activeTab, setActiveTab] = useState<TabType>('info')
  const [isProfileExpanded, setIsProfileExpanded] = useState(false)
  const [isMenuExpanded, setIsMenuExpanded] = useState(false)
  const [isMenuAnimating, setIsMenuAnimating] = useState(false)

  // 记录每个tab的滚动位置
  const [tabScrollPositions, setTabScrollPositions] = useState<Record<TabType, number>>({
    info: 0,
    dynamics: 0,
    cases: 0,
    articles: 0
  })




  // 切换关注状态
  const toggleFollow = () => {
    setIsFollowed(!isFollowed)
  }

  // 切换简介展开状态
  const toggleProfile = () => {
    setIsProfileExpanded(!isProfileExpanded)
  }

  // 保存当前tab的滚动位置
  const saveCurrentScrollPosition = () => {
    return new Promise<void>((resolve) => {
      // 获取页面滚动位置
      Taro.createSelectorQuery()
        .selectViewport()
        .scrollOffset()
        .exec((res) => {
          if (res && res[0]) {
            const scrollTop = res[0].scrollTop
            setTabScrollPositions(prev => ({
              ...prev,
              [activeTab]: scrollTop
            }))
            console.log(`保存 ${activeTab} tab 滚动位置:`, scrollTop)
          }
          resolve()
        })
    })
  }

  // 恢复指定tab的滚动位置
  const restoreScrollPosition = (tab: TabType) => {
    const savedPosition = tabScrollPositions[tab] || 0
    console.log(`恢复 ${tab} tab 滚动位置:`, savedPosition)

    if (savedPosition > 0) {
      // 延迟执行，确保页面内容已渲染
      setTimeout(() => {
        Taro.pageScrollTo({
          scrollTop: savedPosition,
          duration: 0 // 不使用动画，直接跳转
        })
      }, 150)
    }
  }

  // 切换tab
  const handleTabChange = async (tab: TabType) => {
    // 保存当前tab的滚动位置
    await saveCurrentScrollPosition()

    // 切换tab
    setActiveTab(tab)

    // 恢复新tab的滚动位置
    setTimeout(() => {
      restoreScrollPosition(tab)
    }, 100) // 延迟确保tab内容已渲染
  }

  // 切换浮动菜单
  const toggleMenu = () => {
    if (isMenuExpanded) {
      // 收起菜单：先设置动画状态，然后延迟隐藏
      setIsMenuAnimating(true)
      // 立即触发收起动画，300ms后完全隐藏
      setTimeout(() => {
        setIsMenuExpanded(false)
        setIsMenuAnimating(false)
      }, 300)
    } else {
      // 展开菜单：直接显示并播放展开动画
      setIsMenuExpanded(true)
      setIsMenuAnimating(false)
    }
  }

  // 平台客服
  const handleCustomerService = () => {
    // 这里可以添加客服功能
    console.log('联系客服')
    // 执行收起动画
    setIsMenuAnimating(true)
    setTimeout(() => {
      setIsMenuExpanded(false)
      setIsMenuAnimating(false)
    }, 300)
  }

  // 申请委托
  const handleApplyDelegate = () => {
    // 这里可以添加申请委托功能
    console.log('申请委托')
    // 执行收起动画
    setIsMenuAnimating(true)
    setTimeout(() => {
      setIsMenuExpanded(false)
      setIsMenuAnimating(false)
    }, 300)
  }


const onClickMoreDynamics = () => {
    Taro.switchTab({
      url: '/pages/dynamics/index'
    })
  }


  // 加载律师详情
  const loadLawyerDetail = async () => {
    try {
      setLoading(true)
      if (userId) {
        const response = await lawyerApi.getLawyerDetail(Number(userId))
        if (response.success && response.data) {
          const { lawyerInfo: data, lawyerCountData: countData, contentNum: contentData } = response.data
          data && setLawyerInfo(data)
          countData && setLawyerCountData(countData)
          contentData && setContentNum(contentData)
        }
      }
    } catch (error) {
      // 模拟数据
      const mockData: LawyerAPI.LawyerInfo = {
        id: 1,
        userId: Number(userId) || 1,
        province: '广东',
        city: '深圳',
        district: '南山区',
        name: '张律师',
        personalProfile: '资深律师，擅长民事诉讼、合同纠纷等法律事务，具有丰富的执业经验。',
        figurePhotoUrl: mockLawyer,
        lawFirm: '深圳市某某律师事务所',
        lawFirmAddress: '广东省深圳市南山区科技园南区某大厦20楼',
        lawyerLevel: 4,
        lawyerField: [
          { id: 1, name: '民事诉讼' },
          { id: 2, name: '合同纠纷' },
          { id: 3, name: '公司法务' }
        ]
      }

      // 模拟律师统计数据
      const mockLawyerCountData: LawyerAPI.LawyerCountDataDto = {
        caseCount: 156,
        caseCategoryGroup: 12,
        topCategory: ['合同纠纷', '民事诉讼', '公司法务']
      }

      // 模拟内容统计数据
      const mockContentNum: LawyerAPI.ContentNumDto = {
        caseNum: 28,
        dynamicsNum: 12,
        articleNum: 15
      }

      setLawyerInfo(mockData)
      setLawyerCountData(mockLawyerCountData)
      setContentNum(mockContentNum)
      console.error('加载律师详情失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadLawyerDetail()
  }, [userId])


  const navLeftSolt = () => {
    return (
      <View className='nav-left'>
        <Image
          className='nav-left__icon'
          src={leftButtonIcon}
          mode='aspectFit'
          onClick={() => navigateBack()}
        />
        <Text className='nav-left__text'>{`${lawyerInfo?.name} 电子名片` || '律师详情'}</Text>
      </View>
    )
  }
  return (
    <View className='lawyer-detail'>
      {/* 固定背景图片 */}
      <View className='lawyer-detail-bg' style={
        `background-image: url(${lawyerBg})`
      }
      ></View>

      <PageLayout
        backgroundColor='transparent'
        navBarBgColor='transparent'
        showNavBorder={false}
        showBack={false}
        navBarLeft={navLeftSolt()}
      >
        <PageContent padded='b'>
          {lawyerInfo && (
            <View className='lawyer-detail__content'>
              {/* 律师头像和基本信息 */}
              <LawyerAvatar
                lawyerInfo={lawyerInfo}
                isFollowed={isFollowed}
                onFollowToggle={toggleFollow}
                className='lawyer-detail__header'
              />

              {/* Tab导航 */}
              <LawyerTabs
                activeTab={activeTab}
                onTabChange={handleTabChange}
              />

              {/* Tab内容 */}
              {activeTab === 'info' && (
                <View className='lawyer-detail__tab-content'>
                  <LawyerInfoTab
                    lawyerInfo={lawyerInfo}
                    lawyerCountData={lawyerCountData}
                    isProfileExpanded={isProfileExpanded}
                    onProfileToggle={toggleProfile}
                    loading={loading}
                  />
                </View>
              )}

              {activeTab === 'dynamics' && (
                <View className='lawyer-detail__tab-content'>
                  <LawyerDynamicsTab
                    lawyerId={Number(userId)}
                    loading={loading}
                  />
                </View>
              )}

              {activeTab === 'cases' && (
                <View className='lawyer-detail__tab-content'>
                  <Text>案例内容</Text>
                </View>
              )}

              {activeTab === 'articles' && (
                <View className='lawyer-detail__tab-content'>
                  <LawyerArticleTab
                    lawyerId={Number(userId)}
                    contentNum={contentNum}
                    loading={loading}
                    onArticleClick={(article) => {
                      console.log('点击文章:', article.title)
                      // 这里可以添加跳转到文章详情页的逻辑
                    }}
                  />
                </View>
              )}
            </View>
          )}
          {activeTab === 'dynamics' && (
            <View className='lawyer-dynamics-list'>
              <View className='lawyer-dynamics-list__header'>
                <View className='lawyer-dynamics-list__title'>
                  <Image className='lawyer-dynamics-list__title-icon' src={CommentIcon} mode='aspectFit' />
                  <Text className='lawyer-dynamics-list__title-text'>推荐动态</Text>
                </View>
                <View className='lawyer-dynamics-list__more' onClick={onClickMoreDynamics}>
                  <Text>更多</Text>
                </View>
              </View>
              <HorizontalDynamicsList filterParams={{ lawyerId: Number(userId) }} />
            </View>
          )}
        </PageContent>
      </PageLayout>

      {/* 浮动操作菜单 */}
      <FloatingMenu
        isExpanded={isMenuExpanded}
        isAnimating={isMenuAnimating}
        onToggle={toggleMenu}
        menuItems={[
          {
            key: 'customer-service',
            label: '平台客服',
            icon: headsetOutlineIcon,
            onClick: handleCustomerService
          },
          {
            key: 'apply-delegate',
            label: '申请委托',
            icon: editIcon,
            onClick: handleApplyDelegate
          }
        ]}
      />
    </View>
  )
}

export default LawyerDetail
