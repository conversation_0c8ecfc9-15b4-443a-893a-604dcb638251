# HorizontalDynamicsList 横向动态列表组件

横向滑动的动态列表组件，展示最多5个动态卡片，支持外部传入筛选条件。

## 功能特性

- 🔄 **横向滑动**: 支持横向滑动浏览动态列表
- 📝 **动态展示**: 展示动态标题、分类、时间和阅读数
- 🎯 **筛选支持**: 支持外部传入筛选条件
- 📱 **响应式设计**: 适配微信小程序界面
- 🎨 **统一样式**: 遵循项目设计规范
- 🔗 **点击跳转**: 支持点击跳转到动态详情页

## 组件结构

```
HorizontalDynamicsList/
├── index.tsx              # 主组件
├── index.scss            # 样式文件
├── types.ts              # 类型定义
└── README.md             # 文档
```

## 基础用法

```tsx
import HorizontalDynamicsList from '@/components/HorizontalDynamicsList'

const HomePage: React.FC = () => {
  return (
    <HorizontalDynamicsList
      filterParams={{
        categoryId: 1,
        keyword: '法律'
      }}
      className="custom-dynamics-list"
    />
  )
}
```

## 组件属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| filterParams | DynamicsAPI.GetDynamicsListRequest | {} | 动态列表筛选条件 |
| className | string | '' | 自定义样式类名 |

## 筛选参数

filterParams 支持以下筛选条件：

```tsx
interface GetDynamicsListRequest {
  page?: number          // 页码
  pageSize?: number      // 每页数量
  categoryId?: number    // 分类ID
  keyword?: string       // 搜索关键词
  lawyerId?: number      // 律师ID
}
```

## 动态卡片信息

每个动态卡片包含：
- **动态标题**: 最多显示3行，超出省略
- **动态分类**: 显示分类名称，使用主题色
- **发布时间**: 格式化为 MM-DD 显示
- **阅读数量**: 显示浏览次数

## 样式特点

- 卡片尺寸：520rpx × 243rpx
- 背景色：#E8F4FD（浅蓝色）
- 圆角边框：14rpx
- 阴影效果：轻微阴影
- 间距：卡片间距30rpx
- 点击效果：缩放动画

## 数据流

1. 组件挂载时自动加载动态列表
2. 根据传入的筛选条件请求API
3. 支持API失败时的模拟数据降级
4. 点击动态卡片跳转到详情页面

## 使用场景

- 首页动态推荐
- 律师详情页相关动态
- 分类页面动态展示
- 搜索结果动态列表

## 注意事项

- 最多展示5个动态卡片
- 需要确保动态API接口正常可用
- 图标资源需要正确引入
- 时间格式化为MM-DD格式显示
- 支持横向滑动，建议在容器宽度足够时使用
