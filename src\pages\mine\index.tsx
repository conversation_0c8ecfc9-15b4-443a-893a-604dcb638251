import React, { useEffect, useState } from 'react'

import { View, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import PageLayout, { PageContent } from '@/components/PageLayout'

// 导入律师等级图标
import layerLevel1Icon from '@/assets/images/layer-level-icon/layer_1.png'
import layerLevel2Icon from '@/assets/images/layer-level-icon/layer_2.png'
import layerLevel3Icon from '@/assets/images/layer-level-icon/layer_3.png'
import layerLevel4Icon from '@/assets/images/layer-level-icon/layer_4.png'
import layerLevel5Icon from '@/assets/images/layer-level-icon/layer_5.png'

// 导入功能图标
import idCardIcon from '@/assets/images/common-icon/id_card.png'
import verifyIcon from '@/assets/images/common-icon/verify.png'
import bookIcon from '@/assets/images/common-icon/book.png'
import starFillIcon from '@/assets/images/common-icon/star_fill.png'
import bagIcon from '@/assets/images/common-icon/bag.png'
import heartFillIcon from '@/assets/images/common-icon/heart_fill.png'
import headsetIcon from '@/assets/images/common-icon/headset.png'
import DefaultAvatar from '@/assets/images/common-icon/avatar.png'
import ChatIcon from '@/assets/images/common-icon/chat.png'
import SendIcon from '@/assets/images/common-icon/send.png'
import BookMarkIcon from '@/assets/images/common-icon/book_mark_color.png'
import BookMarkFillIcon from '@/assets/images/common-icon/book_mark_fill.png'
import TelegramIcon from '@/assets/images/common-icon/telegram.png'
import NotificationIcon from '@/assets/images/common-icon/notification.png'
import './index.scss'

const Mine: React.FC = () => {
  // 登录状态管理
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [userInfo, setUserInfo] = useState({
    name: '',
    avatar: '',
    lawyerLevel: 1,
    location: '',
    firm: ''
  })

  // 获取律师等级图标
  const getLevelIcon = (lawyerLevel: number): string => {
    const levelIconMap = {
      1: layerLevel1Icon,
      2: layerLevel2Icon,
      3: layerLevel3Icon,
      4: layerLevel4Icon,
      5: layerLevel5Icon
    }
    return levelIconMap[lawyerLevel] || layerLevel1Icon
  }

  // 处理头像点击 - 登录/查看个人信息
  const handleAvatarClick = () => {
    if (!isLoggedIn) {
      // 未登录，跳转到登录页面
      Taro.navigateTo({ url: '/subpackages/login/index' })
    }
  }

  // 开发测试：长按头像切换登录状态
  const handleAvatarLongPress = () => {
    if (isLoggedIn) {
      simulateLogout()
    } else {
      simulateLogin()
    }
  }

  // 模拟登录功能
  const simulateLogin = () => {
    setIsLoggedIn(true)
    setUserInfo({
      name: '张三律师',
      avatar: '',
      lawyerLevel: 4,
      location: '北京市朝阳区',
      firm: '北京市某某律师事务所'
    })
    Taro.showToast({
      title: '登录成功',
      icon: 'success'
    })
  }

  // 模拟退出登录
  const simulateLogout = () => {
    setIsLoggedIn(false)
    setUserInfo({
      name: '',
      avatar: '',
      lawyerLevel: 1,
      location: '',
      firm: ''
    })
    Taro.showToast({
      title: '已退出登录',
      icon: 'success'
    })
  }

  // 检查登录状态
  const checkLoginStatus = () => {
    // 这里可以从本地存储或接口获取登录状态
    const token = Taro.getStorageSync('token')
    if (token) {
      // 模拟已登录状态
      simulateLogin()
    }
  }

  // 菜单项点击处理函数
  const handleMenuClick = (menuType: string) => {
    console.log('点击菜单:', menuType)

    switch (menuType) {
      case 'electronic-card':
        // 跳转到电子名片页面
        Taro.navigateTo({ url: '/subpackages/electronic-card/index' })
        break
      case 'lawyer-auth':
        // 跳转到律师认证页面
        Taro.navigateTo({ url: '/subpackages/lawyer-auth/index' })
        break
      case 'publish-dynamic':
        // 跳转到发布动态页面
        Taro.navigateTo({ url: '/subpackages/publish-dynamic/index' })
        break
      case 'publish-case':
        // 跳转到发布案例页面
        Taro.navigateTo({ url: '/subpackages/publish-case/index' })
        break
      case 'dynamic-manage':
        // 跳转到动态管理页面
        Taro.navigateTo({ url: '/subpackages/dynamic-manage/index' })
        break
      case 'article-manage':
        // 跳转到文章管理页面
        Taro.navigateTo({ url: '/subpackages/article-manage/index' })
        break
      case 'case-manage':
        // 跳转到案例管理页面
        Taro.navigateTo({ url: '/subpackages/case-manage/index' })
        break
      case 'personal-collection':
        // 跳转到个人收藏页面
        Taro.navigateTo({ url: '/subpackages/personal-collection/index' })
        break
      case 'my-cases':
        // 跳转到我的案件页面
        Taro.navigateTo({ url: '/subpackages/my-cases/index' })
        break
      case 'my-follow':
        // 跳转到我的关注页面
        Taro.navigateTo({ url: '/subpackages/my-follow/index' })
        break
      case 'contact-service':
        // 联系客服
        Taro.makePhoneCall({ phoneNumber: '************' })
        break
      case 'notification':
        // 跳转到通知页面
        Taro.navigateTo({ url: '/subpackages/notification/index' })
        break
      default:
        Taro.showToast({
          title: '功能开发中',
          icon: 'none'
        })
    }
  }

  // 页面生命周期
  useEffect(() => {
    console.log('我的页面加载完成')
    checkLoginStatus()

    return () => {
      console.log('我的页面卸载')
    }
  }, [])

  // 页面显示时触发
  Taro.useDidShow(() => {
    console.log('我的页面显示')
  })

  // 页面隐藏时触发
  Taro.useDidHide(() => {
    console.log('我的页面隐藏')
  })

  return (
    <View className='mine-page'>
      {/* 固定背景图片 */}
      <View className='mine-bg'></View>

      <PageLayout
        title='用户中心'
        showBack={false}
        backgroundColor='transparent'
        navBarBgColor='transparent'
        showNavBorder={false}
      >
        <PageContent padded='b'>
          <View className='mine-content'>
            <View className='user-section'>
              {/* 左侧用户信息模块 */}
              <View className='user-info-module'>
                <View
                  className='user-avatar'
                  onClick={handleAvatarClick}
                  onLongPress={handleAvatarLongPress}
                >
                  <Image
                    src={userInfo.avatar || DefaultAvatar}
                    className='avatar-image'
                    mode='aspectFill'
                  />
                </View>
                <View className='user-info'>
                  {isLoggedIn ? (
                    // 已登录状态
                    <>
                      <View className='user-name-level'>
                        <View className='user-name'>{userInfo.name}</View>
                        <Image
                          className='lawyer-level-icon'
                          src={getLevelIcon(userInfo.lawyerLevel)}
                          mode='aspectFit'
                        />
                      </View>
                      <View className='user-location'>{userInfo.location}</View>
                      <View className='user-firm'>{userInfo.firm}</View>
                    </>
                  ) : (
                    // 未登录状态
                    <>
                      <View className='user-name-level'>
                        <View className='user-name'>点击头像登录</View>
                      </View>
                      <View className='user-desc'>登录后享受更多专业服务</View>
                    </>
                  )}
                </View>
              </View>

              {/* 右侧消息提示图标 - 仅登录后显示 */}
              {/* {isLoggedIn && (
                <View className='message-icon'>
                  <Image
                    src={ChatIcon}
                    className='chat-icon'
                    mode='aspectFit'
                  />
                  <View className='message-badge'>3</View>
                </View>
              )} */}
            </View>

            <View className='menu-section'>
              {/* 便捷功能模块 */}
              <View className='menu-module'>
                <View className='module-title'>便捷功能</View>
                <View className='module-content'>
                  <View className='menu-item' onClick={() => handleMenuClick('electronic-card')}>
                    <Image className='menu-icon' src={idCardIcon} mode='aspectFit' />
                    <View className='menu-text'>电子名片</View>
                  </View>
                  <View className='menu-item' onClick={() => handleMenuClick('lawyer-auth')}>
                    <Image className='menu-icon' src={verifyIcon} mode='aspectFit' />
                    <View className='menu-text'>律师认证</View>
                  </View>
                  <View className='menu-item' onClick={() => handleMenuClick('publish-dynamic')}>
                    <Image className='menu-icon' src={SendIcon} mode='aspectFit' />
                    <View className='menu-text'>发布动态</View>
                  </View>
                  <View className='menu-item' onClick={() => handleMenuClick('publish-case')}>
                    <Image className='menu-icon' src={BookMarkIcon} mode='aspectFit' />
                    <View className='menu-text'>发布案例</View>
                  </View>
                </View>
              </View>

              {/* 内容管理模块 */}
              <View className='menu-module'>
                <View className='module-title'>内容管理</View>
                <View className='module-content'>
                  <View className='menu-item' onClick={() => handleMenuClick('dynamic-manage')}>
                    <Image className='menu-icon' src={TelegramIcon} mode='aspectFit' />
                    <View className='menu-text'>动态管理</View>
                  </View>
                  <View className='menu-item' onClick={() => handleMenuClick('article-manage')}>
                    <Image className='menu-icon' src={bookIcon} mode='aspectFit' />
                    <View className='menu-text'>文章管理</View>
                  </View>
                  <View className='menu-item' onClick={() => handleMenuClick('case-manage')}>
                    <Image className='menu-icon' src={BookMarkFillIcon} mode='aspectFit' />
                    <View className='menu-text'>案例管理</View>
                  </View>
                  <View className='menu-item' onClick={() => handleMenuClick('personal-collection')}>
                    <Image className='menu-icon' src={starFillIcon} mode='aspectFit' />
                    <View className='menu-text'>个人收藏</View>
                  </View>
                </View>
              </View>

              {/* 服务管理模块 */}
              <View className='menu-module'>
                <View className='module-title'>服务管理</View>
                <View className='module-content'>
                  <View className='menu-item' onClick={() => handleMenuClick('my-cases')}>
                    <Image className='menu-icon' src={bagIcon} mode='aspectFit' />
                    <View className='menu-text'>我的案件</View>
                  </View>
                  <View className='menu-item' onClick={() => handleMenuClick('my-follow')}>
                    <Image className='menu-icon' src={heartFillIcon} mode='aspectFit' />
                    <View className='menu-text'>我的关注</View>
                  </View>
                  <View className='menu-item' onClick={() => handleMenuClick('contact-service')}>
                    <Image className='menu-icon' src={headsetIcon} mode='aspectFit' />
                    <View className='menu-text'>联系客服</View>
                  </View>
                  {/* <View className='menu-item' onClick={() => handleMenuClick('notification')}>
                    <Image className='menu-icon' src={NotificationIcon} mode='aspectFit' />
                    <View className='menu-text'>通知</View>
                  </View> */}
                </View>
              </View>
            </View>
          </View>
        </PageContent>
      </PageLayout>
    </View>
  )
}

export default Mine
