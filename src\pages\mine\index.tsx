import React, { useEffect, useMemo, useState } from 'react'

import { View, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import PageLayout, { PageContent } from '@/components/PageLayout'
import { lawyer<PERSON><PERSON> } from '@/utils/request/apis/lawyer'

// 导入律师等级图标
import layerLevel1Icon from '@/assets/images/layer-level-icon/layer_1.png'
import layerLevel2Icon from '@/assets/images/layer-level-icon/layer_2.png'
import layerLevel3Icon from '@/assets/images/layer-level-icon/layer_3.png'
import layerLevel4Icon from '@/assets/images/layer-level-icon/layer_4.png'
import layerLevel5Icon from '@/assets/images/layer-level-icon/layer_5.png'

// 导入功能图标
import idCardIcon from '@/assets/images/common-icon/id_card.png'
import verifyIcon from '@/assets/images/common-icon/verify.png'
import bookIcon from '@/assets/images/common-icon/book.png'
import starFillIcon from '@/assets/images/common-icon/star_fill.png'
import bagIcon from '@/assets/images/common-icon/bag.png'
import heartFillIcon from '@/assets/images/common-icon/heart_fill.png'
import headsetIcon from '@/assets/images/common-icon/headset.png'
import DefaultAvatar from '@/assets/images/common-icon/avatar.png'
import SendIcon from '@/assets/images/common-icon/send.png'
import BookMarkIcon from '@/assets/images/common-icon/book_mark_color.png'
import BookMarkFillIcon from '@/assets/images/common-icon/book_mark_fill.png'
import TelegramIcon from '@/assets/images/common-icon/telegram.png'
import bookOpenIcon from '@/assets/images/common-icon/book_open.png'

import './index.scss'

interface MenuModules {
  title: string
  items: MenuModulesItem[]
}

interface MenuModulesItem {
  key: string
  icon: string
  text: string
}

// 菜单配置数据
const defalutMenuModules: MenuModules[] = [
  {
    title: '便捷功能',
    items: [
      {
        key: 'lawyer-auth',
        icon: verifyIcon,
        text: '律师认证'
      },
      {
        key: 'publish-dynamic',
        icon: SendIcon,
        text: '发布动态'
      },
      {
        key: 'publish-article',
        icon: bookOpenIcon,
        text: '发布文章'
      },
      {
        key: 'publish-case',
        icon: BookMarkIcon,
        text: '发布案例'
      }
    ]
  },
  {
    title: '内容管理',
    items: [
      {
        key: 'personal-collection',
        icon: starFillIcon,
        text: '个人收藏'
      },
      {
        key: 'dynamic-manage',
        icon: TelegramIcon,
        text: '动态管理'
      },
      {
        key: 'article-manage',
        icon: bookIcon,
        text: '文章管理'
      },
      {
        key: 'case-manage',
        icon: BookMarkFillIcon,
        text: '案例管理'
      }
    ]
  },
  {
    title: '服务管理',
    items: [
      {
        key: 'my-cases',
        icon: bagIcon,
        text: '我的案件'
      },
      {
        key: 'my-follow',
        icon: heartFillIcon,
        text: '我的关注'
      },
      {
        key: 'contact-service',
        icon: headsetIcon,
        text: '联系客服'
      },
      {
        key: '-',
        icon: '',
        text: ''
      }
    ]
  }
]

const Mine: React.FC = () => {
  // 登录状态管理
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [menuModules, setMenuModules] = useState<MenuModules[]>(defalutMenuModules)
  const [userInfo, setUserInfo] = useState<UserAPI.UserInfo>({
    id: 0,
    userName: '',
    nickName: '',
    avatarUrl: '',
    lastLoginTime: ''
  })
  const [lawyerInfo, setLawyerInfo] = useState<LawyerAPI.GetOwnerLawyerInfoResponse>({
    userId: 0,
    province: '',
    city: '',
    district: '',
    name: '',
    lawFirm: '',
    lawFirmAddress: '',
    lawyerLevel: 0,
  } as LawyerAPI.GetOwnerLawyerInfoResponse)

  const authLawyer = () => {
    const electronicCard = {
      key: 'electronic-card',
      icon: idCardIcon,
      text: '电子名片'
    }
    setMenuModules((prev) => {
      prev[0].items[0] = electronicCard
      return prev
    })
  }

  const getLayerData = async () => {
    try {
      const response = await lawyerApi.getOwnerLawyerInfo()
      if (response.success && response.data) {
        setLawyerInfo(response.data)
        if (response.data.personalProfile) {
          authLawyer()
        }
      }
    } catch (error) {
      console.error('获取律师信息失败:', error)
      //mock
      setLawyerInfo({
        userId: 1,
        province: '广东',
        city: '深圳',
        district: '南山区',
        name: '张律师',
        lawFirm: '深圳市某某律师事务所',
        lawFirmAddress: '广东省深圳市南山区科技园南区某大厦20楼',
        lawyerLevel: 4,
        personalProfile: '个人简介',
      } as LawyerAPI.GetOwnerLawyerInfoResponse)
    }
  }

  // 获取律师等级图标
  const getLevelIcon = (lawyerLevel: number): string => {
    const levelIconMap = {
      1: layerLevel1Icon,
      2: layerLevel2Icon,
      3: layerLevel3Icon,
      4: layerLevel4Icon,
      5: layerLevel5Icon
    }
    return levelIconMap[lawyerLevel] || layerLevel1Icon
  }



  // 处理头像点击 - 登录/查看个人信息
  const handleAvatarClick = () => {
    if (!isLoggedIn) {
      // 未登录，跳转到登录页面
      Taro.navigateTo({ url: '/subpackages/login/index' })
    }
  }

  // 开发测试：长按头像切换登录状态
  const handleAvatarLongPress = () => {
    if (isLoggedIn) {
      simulateLogout()
    } else {
      simulateLogin()
    }
  }

  // 模拟登录功能
  const simulateLogin = () => {
    setIsLoggedIn(true)
    setUserInfo({
      id: 1,
      userName: 'zhangsan',
      nickName: '张三律师',
      avatarUrl: '',
      lastLoginTime: ''
    })
    Taro.showToast({
      title: '登录成功',
      icon: 'success'
    })
  }

  // 模拟退出登录
  const simulateLogout = () => {
    setIsLoggedIn(false)
    setUserInfo({
      name: '',
      avatar: '',
      lawyerLevel: 1,
      location: '',
      firm: ''
    })
    Taro.showToast({
      title: '已退出登录',
      icon: 'success'
    })
  }

  // 检查登录状态
  const checkLoginStatus = () => {
    // 这里可以从本地存储或接口获取登录状态
    const token = Taro.getStorageSync('token')
    if (token) {
      // 模拟已登录状态
      simulateLogin()
    }
  }

  // 菜单项点击处理函数
  const handleMenuClick = (menuType: string) => {
    switch (menuType) {
      case 'electronic-card':
        // 跳转到电子名片页面
        Taro.navigateTo({ url: `/subpackages/detail/lawyer/index?id=${lawyerInfo.userId}` })
        break
      case 'lawyer-auth':
        // 跳转到律师认证页面
        Taro.navigateTo({ url: `/subpackages/lawyer-entry/index?id=${lawyerInfo.userId}` })
        break
      case 'publish-article':
        // 跳转到发布文章页面
        Taro.navigateTo({ url: `/subpackages/publish-article/index?id=${lawyerInfo.userId}` })
        break
      case 'publish-dynamic':
        // 跳转到发布动态页面
        Taro.navigateTo({ url: `/subpackages/publish-dynamic/index?id=${lawyerInfo.userId}` })
        break
      case 'publish-case':
        // 跳转到发布案例页面
        Taro.navigateTo({ url: `/subpackages/publish-case/index?id=${lawyerInfo.userId}` })
        break
      case 'dynamic-manage':
        // 跳转到动态管理页面
        Taro.navigateTo({ url: `/subpackages/lawyer-dynamics-manage/index` })
        break
      case 'article-manage':
        // 跳转到文章管理页面
        Taro.navigateTo({ url: `/subpackages/lawyer-article-manage/index` })
        break
      case 'case-manage':
        // 跳转到案例管理页面
        Taro.navigateTo({ url: `/subpackages/lawyer-case-manage/index` })
        break
      case 'personal-collection':
        // 跳转到个人收藏页面
        Taro.navigateTo({ url: `/subpackages/article-collection/index` })
        break
      case 'my-cases':
        // 跳转到我的案件页面
        Taro.navigateTo({ url: `/subpackages/my-cases/index` })
        break
      case 'my-follow':
        // 跳转到我的关注页面
        Taro.navigateTo({ url: `/subpackages/my-follow/index` })
        break
      case 'contact-service':
        // 联系客服
        Taro.makePhoneCall({ phoneNumber: '************' })
        break
      // case 'notification':
      //   // 跳转到通知页面
      //   Taro.navigateTo({ url: '/subpackages/notification/index' })
      //   break
      default:
        Taro.showToast({
          title: '功能开发中',
          icon: 'none'
        })
    }
  }

  // 页面生命周期
  useEffect(() => {
    checkLoginStatus()
    getLayerData()
  }, [])

  const lawyerLocation = useMemo(() => {
    // 普通用户无地址
    if (lawyerInfo.province) {
      return `${lawyerInfo.province}${lawyerInfo.city}${lawyerInfo.district}`
    } else {
      return ''
    }
  }, [lawyerInfo])

  return (
    <View className='mine-page'>
      {/* 固定背景图片 */}
      <View className='mine-bg'></View>

      <PageLayout
        title='用户中心'
        showBack={false}
        backgroundColor='transparent'
        navBarBgColor='transparent'
        showNavBorder={false}
      >
        <PageContent padded='b'>
          <View className='mine-content'>
            <View className='user-section'>
              {/* 左侧用户信息模块 */}
              <View className='user-info-module'>
                <View
                  className='user-avatar'
                  onClick={handleAvatarClick}
                  onLongPress={handleAvatarLongPress}
                >
                  <Image
                    src={userInfo.avatarUrl || DefaultAvatar}
                    className='avatar-image'
                    mode='aspectFill'
                  />
                </View>
                <View className='user-info'>
                  {isLoggedIn ? (
                    // 已登录状态
                    <>
                      <View className='user-name-level'>
                        <View className='user-name'>{userInfo.nickName}</View>
                        {lawyerInfo.lawyerLevel ? <Image
                          className='lawyer-level-icon'
                          src={getLevelIcon(lawyerInfo.lawyerLevel)}
                          mode='aspectFit'
                        /> : null}
                      </View>
                      {lawyerLocation ? <View className='user-location'>{lawyerLocation}</View> : null}
                      {lawyerInfo.lawFirm ? <View className='user-firm'>{lawyerInfo.lawFirm}</View> : null}
                    </>
                  ) : (
                    // 未登录状态
                    <>
                      <View className='user-name-level'>
                        <View className='user-name'>点击头像登录</View>
                      </View>
                      <View className='user-desc'>登录后享受更多专业服务</View>
                    </>
                  )}
                </View>
              </View>

              {/* 右侧消息提示图标 - 仅登录后显示 */}
              {/* {isLoggedIn && (
                <View className='message-icon'>
                  <Image
                    src={ChatIcon}
                    className='chat-icon'
                    mode='aspectFit'
                  />
                  <View className='message-badge'>3</View>
                </View>
              )} */}
            </View>

            <View className='menu-section'>
              {menuModules.map((module, moduleIndex) => (
                <View key={moduleIndex} className='menu-module'>
                  <View className='module-title'>{module.title}</View>
                  <View className='module-content'>
                    {module.items.map((item, itemIndex) => (
                      <View
                        key={itemIndex}
                        className='menu-item'
                        onClick={() => handleMenuClick(item.key)}
                      >
                        <Image className='menu-icon' src={item.icon} mode='aspectFit' />
                        <View className='menu-text'>{item.text}</View>
                      </View>
                    ))}
                  </View>
                </View>
              ))}
            </View>
          </View>
        </PageContent>
      </PageLayout>
    </View>
  )
}

export default Mine
