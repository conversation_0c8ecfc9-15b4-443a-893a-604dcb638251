/**
 * 导航栏组件
 *
 * 支持自定义标题、左右插槽、返回按钮等功能
 */
import React from 'react'
import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { NavBarProps } from './types'
import { useNavBarHeight, getNavBarStyle, getContentStyle } from './hooks/useNavBarHeight'
import './index.scss'

const NavBar: React.FC<NavBarProps> = ({
  title,
  showBack = true,
  onBack,
  backgroundColor = '#ffffff',
  textColor = '#BD8A4F',
  showBorder = true,
  leftSlot,
  rightSlot,
  centerSlot,
  showLogo = false,
  logoSrc = '/assets/images/logo.png',
  fixed = true,
  className = ''
}) => {
  // 使用自定义 Hook 获取导航栏高度信息
  const { navBarHeight, contentHeight, statusBarHeight } = useNavBarHeight()

  // 处理返回按钮点击
  const handleBack = () => {
    if (onBack) {
      onBack()
    } else {
      Taro.navigateBack()
    }
  }

  // 渲染中间内容
  const renderCenter = () => {
    if (centerSlot) {
      return centerSlot
    }

    if (showLogo) {
      return (
        <Image
          className="nav-bar__logo"
          src={logoSrc}
          mode="aspectFit"
        />
      )
    }

    if (title) {
      return (
        <Text
          className="nav-bar__title"
          style={{ color: textColor }}
        >
          {title}
        </Text>
      )
    }

    return null
  }

  return (
    <View
      className={`nav-bar ${fixed ? 'nav-bar--fixed' : ''} ${showBorder ? 'nav-bar--border' : ''} ${className}`}
      style={getNavBarStyle(navBarHeight, backgroundColor, fixed)}
    >
      <View
        className="nav-bar__content"
        style={getContentStyle(statusBarHeight, contentHeight)}
      >
        {/* 左侧区域 */}
        <View className="nav-bar__left">
          {leftSlot ? (
            leftSlot
          ) : showBack ? (
            <View
              className="nav-bar__back"
              onClick={handleBack}
            >
              <View className="nav-bar__back-icon" />
              <Text
                className="nav-bar__back-text"
                style={{ color: textColor }}
              >
                返回
              </Text>
            </View>
          ) : null}
        </View>

        {/* 中间区域 */}
        <View className="nav-bar__center">
          {renderCenter()}
        </View>

        {/* 右侧区域 */}
        <View className="nav-bar__right">
          {rightSlot}
        </View>
      </View>
    </View>
  )
}

// 导出类型
export type {
  NavBarProps,
  NavBarTheme,
  NavBarConfig,
  NavBarStyle,
  NavBarButton,
  NavBarAction
} from './types'

export default NavBar
