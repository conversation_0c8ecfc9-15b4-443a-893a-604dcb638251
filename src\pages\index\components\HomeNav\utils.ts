/**
 * HomeNav 组件工具函数
 */
import { navigateToPage } from '@/utils'
import Taro from '@tarojs/taro'
import { NavItem } from './types'

/**
 * 处理导航项点击事件
 * @param item 导航项
 * @param index 索引
 * @param customHandler 自定义处理函数
 */
export const handleNavItemClick = async (
  item: NavItem,
  index: number,
  customHandler?: (item: NavItem, index: number) => void
): Promise<void> => {
  // 如果有自定义处理函数，优先执行
  if (customHandler) {
    customHandler(item, index)
    return
  }

  // 如果没有跳转链接，不执行任何操作
  if (!item.linkUrl) {
    console.log(`点击了 ${item.title}，但没有配置跳转链接`)
    return
  }

  // 执行页面跳转
  try {
    await navigateToPage(item.linkUrl)
  } catch (error) {
    console.error('导航跳转失败:', error)
    Taro.showToast({
      title: '跳转失败',
      icon: 'error'
    })
  }
}

/**
 * 生成默认导航项
 * @param iconPrefix 图标前缀路径
 * @param titles 标题数组
 * @returns 导航项数组
 */
export const generateDefaultNavItems = (
  iconPrefix: string = '/assets/images/home-nav-icon/',
  titles: string[] = [
    '黄金救援',
    '优配律师',
    '品牌律师',
    '金牌律师',
    '我的案件',
    '履约保证',
    '律师入驻',
    '关于我们'
  ]
): NavItem[] => {
  return titles.map((title, index) => ({
    id: index + 1,
    iconSrc: `${iconPrefix}home_nav_${index + 1}.png`,
    title,
    linkUrl: '' // 默认为空，需要后续配置
  }))
}

/**
 * 验证导航项数据
 * @param navItems 导航项数组
 * @returns 验证结果
 */
export const validateNavItems = (navItems: NavItem[]): {
  isValid: boolean
  errors: string[]
} => {
  const errors: string[] = []

  if (!Array.isArray(navItems)) {
    errors.push('navItems 必须是数组')
    return { isValid: false, errors }
  }

  if (navItems.length === 0) {
    errors.push('navItems 数组不能为空')
    return { isValid: false, errors }
  }

  if (navItems.length > 8) {
    errors.push('navItems 数组长度不能超过8个')
  }

  navItems.forEach((item, index) => {
    if (!item.iconSrc) {
      errors.push(`第 ${index + 1} 个导航项缺少 iconSrc`)
    }

    if (!item.title) {
      errors.push(`第 ${index + 1} 个导航项缺少 title`)
    }

    if (item.title && item.title.length > 4) {
      errors.push(`第 ${index + 1} 个导航项标题过长，建议不超过4个字符`)
    }

    if (item.iconSrc && typeof item.iconSrc !== 'string') {
      errors.push(`第 ${index + 1} 个导航项的 iconSrc 必须是字符串`)
    }

    if (item.title && typeof item.title !== 'string') {
      errors.push(`第 ${index + 1} 个导航项的 title 必须是字符串`)
    }

    if (item.linkUrl && typeof item.linkUrl !== 'string') {
      errors.push(`第 ${index + 1} 个导航项的 linkUrl 必须是字符串`)
    }
  })

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 格式化导航项数据
 * @param rawData 原始数据
 * @returns 格式化后的导航项数据
 */
export const formatNavItems = (rawData: any[]): NavItem[] => {
  if (!Array.isArray(rawData)) {
    return []
  }

  return rawData
    .filter(item => item && (item.iconSrc || item.icon) && (item.title || item.name))
    .slice(0, 8) // 最多8个
    .map((item, index) => ({
      id: item.id || index + 1,
      iconSrc: item.iconSrc || item.icon,
      title: item.title || item.name,
      linkUrl: item.linkUrl || item.link || item.url || ''
    }))
}

/**
 * 获取导航项的唯一 key
 * @param item 导航项
 * @param index 索引
 * @returns 唯一 key
 */
export const getNavItemKey = (item: NavItem, index: number): string => {
  return item.id ? String(item.id) : `nav-item-${index}-${item.title}`
}

/**
 * 检查图标是否存在
 * @param iconSrc 图标路径
 * @returns Promise<boolean>
 */
export const checkIconExists = (iconSrc: string): Promise<boolean> => {
  return new Promise((resolve) => {
    Taro.getImageInfo({
      src: iconSrc,
      success: () => resolve(true),
      fail: () => resolve(false)
    })
  })
}

/**
 * 批量检查图标是否存在
 * @param navItems 导航项数组
 * @returns Promise<boolean[]>
 */
export const batchCheckIcons = async (navItems: NavItem[]): Promise<boolean[]> => {
  const promises = navItems.map(item => checkIconExists(item.iconSrc))
  return Promise.all(promises)
}

/**
 * 埋点统计
 * @param item 导航项
 * @param index 索引
 * @param action 操作类型
 */
export const trackNavItemEvent = (
  item: NavItem,
  index: number,
  action: 'click' | 'view' | 'error' = 'click'
): void => {
  // 这里可以接入具体的埋点SDK
  console.log('导航埋点:', {
    action,
    item_id: item.id,
    item_title: item.title,
    item_index: index,
    timestamp: Date.now()
  })
}
