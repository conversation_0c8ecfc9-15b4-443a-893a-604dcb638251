/**
 * 律师详情Tab导航组件
 * 
 * 提供信息、动态、案例、文章四个Tab切换
 */
import React from 'react'
import { View, Text } from '@tarojs/components'
import './index.scss'

// Tab类型定义
export type TabType = 'info' | 'dynamics' | 'cases' | 'articles'

// Tab配置
interface TabConfig {
  key: TabType
  label: string
}

// 组件属性
export interface LawyerTabsProps {
  // 当前激活的Tab
  activeTab: TabType
  // Tab切换回调
  onTabChange: (tab: TabType) => void
  // 自定义Tab配置
  tabs?: TabConfig[]
  // 自定义样式类名
  className?: string
}

// 默认Tab配置
const DEFAULT_TABS: TabConfig[] = [
  { key: 'info', label: '信息' },
  { key: 'dynamics', label: '动态' },
  { key: 'cases', label: '案例' },
  { key: 'articles', label: '文章' }
]

const LawyerTabs: React.FC<LawyerTabsProps> = ({
  activeTab,
  onTabChange,
  tabs = DEFAULT_TABS,
  className = ''
}) => {
  return (
    <View className={`lawyer-tabs ${className}`}>
      {tabs.map((tab) => (
        <View
          key={tab.key}
          className={`lawyer-tabs__tab ${activeTab === tab.key ? 'lawyer-tabs__tab--active' : ''}`}
          onClick={() => onTabChange(tab.key)}
        >
          <Text className='lawyer-tabs__tab-text'>{tab.label}</Text>
        </View>
      ))}
    </View>
  )
}

export default LawyerTabs
