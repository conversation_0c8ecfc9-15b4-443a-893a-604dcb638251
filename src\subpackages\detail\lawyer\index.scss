/**
 * 律师详情页面样式
 */

// 导航栏左侧样式
.nav-left {
  display: flex;
  align-items: center;

  &__icon {
    width: 44rpx;
    height: 44rpx;
    padding-left: 30rpx;
    padding-right: 20rpx;
  }

  &__text {
    font-weight: 800;
    font-size: 36rpx;
    color: #000000;
  }
}

.lawyer-detail {
  position: relative;
}

// 固定背景图片
.lawyer-detail-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--taro-nav-height, 420rpx) + 280rpx); // 导航栏高度 + 280rpx
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: -1;
}

.lawyer-detail {
  &__content {
    padding: 30rpx 30rpx 0;
  }

  // 头部信息区域
  &__header {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx 0;
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  }

  &__avatar {
    position: relative;
    width: 690rpx;
    height: 960rpx;
    margin: 0 30rpx;
    overflow: hidden;
    flex-shrink: 0;
  }

  &__avatar-img {
    width: 100%;
    height: 100%;
  }

  // 右上角认证图标
  &__verify {
    position: absolute;
    top: 30rpx;
    right: 30rpx;
  }

  &__verify-icon {
    width: 64rpx;
    height: 64rpx;
  }

  // 律师等级图标
  &__level-badge {
    position: absolute;
    bottom: 300rpx;
    right: 30rpx;
  }

  &__level-badge-icon {
    max-width: 296rpx;
    height: 52rpx;
  }

  // 姓名和关注按钮
  &__name-follow {
    position: absolute;
    bottom: 180rpx;
    left: 30rpx;
    right: 30rpx;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
  }

  &__name-text {
    font-size: 36rpx;
    font-weight: 800;
    color: #ffffff;
  }

  &__follow-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
  }

  &__follow-icon {
    width: 48rpx;
    height: 48rpx;
  }

  &__follow-text {
    font-size: 24rpx;
    color: #ffffff;
    text-align: center;
  }

  // 聚焦信息框
  &__focus {
    position: absolute;
    bottom: 30rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 612rpx;
    height: 102rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__focus-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 1;
  }

  &__focus-text {
    position: relative;
    z-index: 2;
    color: #ffffff;
    font-size: 28rpx;
    text-align: center;
    padding: 0 30rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
  }

  // Tab导航
  &__tabs {
    display: flex;
    background-color: #ffffff;
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    padding: 10rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  }

  &__tab {
    flex: 1;
    text-align: center;
    padding: 20rpx 0;
    border-radius: 15rpx;
    transition: all 0.3s ease;

    &--active {
      background-color: #BD8A4F;
    }
  }

  &__tab-text {
    font-size: 28rpx;
    font-weight: 500;
    color: #666666;

    .lawyer-detail__tab--active & {
      color: #ffffff;
      font-weight: 600;
    }
  }

  // Tab内容
  &__tab-content {
    // 内容区域样式
  }

  // 个人简介区域
  &__profile {
    background-color: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  }

  &__profile-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #000000;
  }

  &__profile-content {
    margin-top: 30rpx;
    max-height: 520rpx;
    overflow: hidden;
    transition: max-height 0.3s ease;

    &--expanded {
      max-height: none;
    }
  }

  &__profile-text {
    font-size: 28rpx;
    color: #333333;
    line-height: 1.6;
  }

  &__profile-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20rpx;
    gap: 10rpx;
  }

  &__profile-icon {
    width: 45rpx;
    height: 45rpx;
  }

  &__profile-toggle-text {
    font-weight: 400;
    font-size: 24rpx;
    color: #727E8C;
    line-height: 42rpx;
  }

  // 擅长领域区域
  &__fields {
    background-color: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  }

  &__fields-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #000000;
  }

  &__fields-list {
    margin-top: 30rpx;
    display: flex;
    flex-wrap: wrap;
    gap: 15rpx;
  }

  &__field-tag {
    width: 160rpx;
    height: 56rpx;
    background: #BD8A4F;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__field-text {
    font-weight: 600;
    font-size: 28rpx;
    color: #FFFFFF;
  }

  // 律师数据卡片
  &__stats-card {
    margin-bottom: 30rpx;
  }



  // 律所信息区域
  &__firm {
    background-color: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  }

  &__firm-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #000000;
  }

  &__firm-item {
    margin-top: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    &:last-child {
      padding-top: 20rpx;
      border-top: 1rpx solid #eee;
      margin-bottom: 0;
    }
  }

  &__firm-label {
    font-size: 24rpx;
    color: #666666;
    font-weight: 400;
  }

  &__firm-value {
    font-size: 26rpx;
    color: #333333;
    font-weight: 500;
    text-align: right;
    flex: 1;
    margin-left: 20rpx;
  }

  // 操作按钮区域
  &__actions {
    display: flex;
    gap: 20rpx;
    padding: 20rpx 0;
  }

  &__btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 600;
    border: none;

    &--primary {
      background-color: #BD8A4F;
      color: #ffffff;

      &::after {
        border: none;
      }
    }

    &--secondary {
      background-color: #ffffff;
      color: #BD8A4F;
      border: 2rpx solid #BD8A4F;

      &::after {
        border: none;
      }
    }
  }

  // 浮动操作菜单
  &__float-menu {
    position: fixed;
    right: 46rpx;
    bottom: 100rpx;
    z-index: 999;
    transition: all 0.1s ease;
  }

  // 菜单按钮
  &__menu-button {
    width: 88rpx;
    height: 88rpx;
    background: #FFFFFF;
    box-shadow: 0rpx 2rpx 48rpx 0rpx rgba(0, 0, 0, 0.04);
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease;

    &--dragging {
      transform: scale(1.1);
      box-shadow: 0rpx 4rpx 60rpx 0rpx rgba(0, 0, 0, 0.1);
    }
  }

  &__menu-button-icon {
    width: 45rpx;
    height: 45rpx;
  }

  // 展开的菜单
  &__menu-expanded {
    display: flex;
    align-items: center;
    background: #FFFFFF;
    box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
    border-radius: 44rpx;
    padding: 0 20rpx;
    gap: 10rpx;
    transform-origin: right center;
    animation: menuSlideIn 0.3s ease-out forwards;

    // 右侧展开（在左半屏时）
    &--right {
      transform-origin: left center;
      animation: menuSlideInRight 0.3s ease-out forwards;
    }
  }

  // 菜单展开动画（向左展开）
  @keyframes menuSlideIn {
    0% {
      transform: scaleX(0);
      opacity: 0;
    }

    100% {
      transform: scaleX(1);
      opacity: 1;
    }
  }

  // 菜单展开动画（向右展开）
  @keyframes menuSlideInRight {
    0% {
      transform: scaleX(0);
      opacity: 0;
    }

    100% {
      transform: scaleX(1);
      opacity: 1;
    }
  }

  // 菜单收起动画
  @keyframes menuSlideOut {
    0% {
      transform: scaleX(1);
      opacity: 1;
    }

    100% {
      transform: scaleX(0);
      opacity: 0;
    }
  }

  // 收起动画类
  &__menu-collapsed {
    display: flex;
    align-items: center;
    background: #FFFFFF;
    box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
    border-radius: 44rpx;
    padding: 0 20rpx;
    gap: 10rpx;
    transform-origin: right center;
    animation: menuSlideOut 0.3s ease-in forwards;

    // 右侧收起（在左半屏时）
    &--right {
      transform-origin: left center;
      animation: menuSlideOutRight 0.3s ease-in forwards;
    }
  }

  // 右侧收起动画
  @keyframes menuSlideOutRight {
    0% {
      transform: scaleX(1);
      opacity: 1;
    }

    100% {
      transform: scaleX(0);
      opacity: 0;
    }
  }

  &__menu-item {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 15rpx 10rpx;

    &--close {
      padding: 9rpx;
    }
  }

  &__menu-icon {
    width: 45rpx;
    height: 45rpx;
  }

  &__menu-text {
    font-size: 24rpx;
    color: #666666;
    white-space: nowrap;
  }

  &__menu-close {
    width: 70rpx;
    height: 70rpx;
  }
}

.lawyer-dynamics-list {
  display: flex;
  flex-direction: column;

  &__header {
    padding: 0 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__title {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    &-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 12rpx;
    }

    &-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #000000;
    }
  }

  &__more {
    text-align: right;
    margin-bottom: 24rpx;

    text {
      font-size: 28rpx;
      color: #828D99;
    }
  }
}