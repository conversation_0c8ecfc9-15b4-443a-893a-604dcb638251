# PageLayout 页面布局组件

一个功能完整的微信小程序页面布局组件，提供统一的页面结构和常用的页面状态处理。

## 功能特性

- 🎨 **统一布局**: 提供标准的页面布局结构
- 🧭 **导航栏集成**: 内置增强的 NavBar 组件，支持 Logo、插槽等
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 🔄 **下拉刷新**: 基于 ScrollView 的原生下拉刷新
- 📄 **触底加载**: 支持触底加载更多数据
- 🎭 **状态管理**: 内置加载、空状态、错误状态组件
- 🛡️ **安全区域**: 自动适配安全区域
- 🎯 **TypeScript**: 完整的类型支持
- 📜 **滚动优化**: 使用 ScrollView 提供流畅的滚动体验
- 📏 **动态高度**: 自动获取导航栏实际高度，确保内容区域正确适配

## 组件结构

```
PageLayout/
├── index.tsx              # 主组件
├── index.scss            # 样式文件
├── types.ts              # 类型定义
├── example.tsx           # 使用示例
├── components/
│   └── PageStates.tsx    # 页面状态组件
└── README.md             # 文档
```

## 基础用法

### 1. 简单页面

```tsx
import PageLayout from '@/components/PageLayout'
import { PageContent, PageCard } from '@/components/PageLayout/components/PageStates'

const SimplePage = () => {
  return (
    <PageLayout title="页面标题">
      <PageContent>
        <PageCard title="卡片标题">
          <Text>页面内容</Text>
        </PageCard>
      </PageContent>
    </PageLayout>
  )
}
```

### 2. 带加载状态的页面

```tsx
import { useState, useEffect } from 'react'
import PageLayout from '@/components/PageLayout'
import { PageLoading, PageContent } from '@/components/PageLayout/components/PageStates'

const LoadingPage = () => {
  const [loading, setLoading] = useState(true)
  const [data, setData] = useState([])

  useEffect(() => {
    // 模拟数据加载
    setTimeout(() => {
      setData([...])
      setLoading(false)
    }, 2000)
  }, [])

  return (
    <PageLayout title="加载页面">
      {loading ? (
        <PageLoading text="加载中..." />
      ) : (
        <PageContent>
          {/* 渲染数据 */}
        </PageContent>
      )}
    </PageLayout>
  )
}
```

### 3. 分页列表页面

```tsx
import { useState } from 'react'
import PageLayout from '@/components/PageLayout'
import { PageContent, PageList, PageLoadMore } from '@/components/PageLayout/components/PageStates'

const ListPage = () => {
  const [list, setList] = useState([])
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)

  const loadMore = () => {
    if (loading) return
    setLoading(true)
    // 加载更多数据逻辑
  }

  return (
    <PageLayout 
      title="列表页面"
      enableReachBottom
      onReachBottom={loadMore}
    >
      <PageContent>
        <PageList>
          {list.map(item => (
            <View key={item.id}>
              {/* 列表项内容 */}
            </View>
          ))}
        </PageList>
        
        <PageLoadMore 
          loading={loading}
          hasMore={hasMore}
          onLoadMore={loadMore}
        />
      </PageContent>
    </PageLayout>
  )
}
```

## API 文档

### PageLayout Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | string | - | 页面标题 |
| showNavBar | boolean | true | 是否显示导航栏 |
| showBack | boolean | true | 是否显示返回按钮 |
| navBarBgColor | string | '#ffffff' | 导航栏背景色 |
| navBarTextColor | string | '#000000' | 导航栏文字颜色 |
| showNavBorder | boolean | true | 是否显示导航栏底部边框 |
| backgroundColor | string | '#f5f5f5' | 页面背景色 |
| contentClassName | string | '' | 内容区域样式类名 |
| enablePullRefresh | boolean | false | 是否启用下拉刷新 |
| onPullRefresh | () => void | - | 下拉刷新回调 |
| enableReachBottom | boolean | false | 是否启用触底加载 |
| onReachBottom | () => void | - | 触底加载回调 |
| navBarLeft | ReactNode | - | 自定义导航栏左侧内容 |
| navBarRight | ReactNode | - | 自定义导航栏右侧内容 |
| fullScreen | boolean | false | 是否全屏显示 |
| safeArea | boolean | true | 是否安全区域适配 |

### 页面状态组件

#### PageLoading

```tsx
<PageLoading text="加载中..." />
```

#### PageEmpty

```tsx
<PageEmpty 
  text="暂无数据"
  description="描述信息"
  actionText="重新加载"
  onAction={() => {}}
/>
```

#### PageError

```tsx
<PageError 
  text="加载失败"
  description="错误描述"
  onRetry={() => {}}
/>
```

#### PageLoadMore

```tsx
<PageLoadMore 
  loading={false}
  hasMore={true}
  onLoadMore={() => {}}
/>
```

## 样式定制

### CSS 变量

```scss
.page-layout {
  --nav-height: 88rpx;
  --content-padding: 32rpx;
  --card-radius: 16rpx;
  --card-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}
```

### 自定义样式类

```scss
.custom-page {
  .page-layout__content {
    padding-top: 120rpx; // 自定义内容区域顶部间距
  }
  
  .page-content__card {
    border-radius: 24rpx; // 自定义卡片圆角
  }
}
```

## 动态高度适配

### 自动高度计算

PageLayout 组件现在会自动获取 NavBar 的实际高度，并动态设置内容区域的 `padding-top`：

```tsx
// 组件内部自动处理
const { navBarHeight } = useNavBarHeight()

<ScrollView
  style={{
    paddingTop: showNavBar && !fullScreen ? `${navBarHeight}px` : '0px'
  }}
>
  {children}
</ScrollView>
```

### 高度适配优势

1. **精确适配**: 根据实际导航栏高度调整，不再使用固定值
2. **设备兼容**: 自动适配不同设备的状态栏和胶囊按钮
3. **配置响应**: 根据导航栏配置（Logo、自定义内容等）动态调整
4. **全屏支持**: 全屏模式下自动移除 padding-top

### 验证方法

```tsx
import { DynamicHeightInfoTest } from '@/components/PageLayout/dynamic-height-test'

// 查看实际高度信息
<DynamicHeightInfoTest />
```

## 最佳实践

### 1. 页面状态管理

```tsx
enum PageState {
  LOADING = 'loading',
  SUCCESS = 'success',
  EMPTY = 'empty',
  ERROR = 'error'
}

const [pageState, setPageState] = useState(PageState.LOADING)

const renderContent = () => {
  switch (pageState) {
    case PageState.LOADING:
      return <PageLoading />
    case PageState.EMPTY:
      return <PageEmpty />
    case PageState.ERROR:
      return <PageError onRetry={handleRetry} />
    default:
      return <PageContent>{/* 正常内容 */}</PageContent>
  }
}
```

### 2. 结合请求库使用

```tsx
import { useRequest } from '@/utils/request/hooks'
import { lawyerApi } from '@/utils/request/apis'

const LawyerListPage = () => {
  const { data, loading, error, refresh } = useRequest(
    () => lawyerApi.getLawyerList({ page: 1, pageSize: 10 }),
    { immediate: true }
  )

  return (
    <PageLayout 
      title="律师列表"
      enablePullRefresh
      onPullRefresh={refresh}
    >
      {loading && <PageLoading />}
      {error && <PageError onRetry={refresh} />}
      {data && data.list.length === 0 && <PageEmpty />}
      {data && data.list.length > 0 && (
        <PageContent>
          {/* 渲染律师列表 */}
        </PageContent>
      )}
    </PageLayout>
  )
}
```

### 3. 性能优化

- 使用 `React.memo` 优化组件渲染
- 合理使用 `useMemo` 和 `useCallback`
- 避免在渲染函数中创建新对象

## 注意事项

1. **导航栏高度**: 默认导航栏高度为 88rpx，如需修改请同步更新样式
2. **安全区域**: 在有底部操作栏的页面建议开启安全区域适配
3. **下拉刷新**: 需要配合具体的刷新逻辑实现
4. **触底加载**: 建议结合分页数据管理使用
5. **性能考虑**: 大列表建议使用虚拟滚动或分页加载
