import { PropsWithChildren, useEffect } from 'react'
import { useDidShow, useDidHide } from '@tarojs/taro'
import './app.scss'

function App({ children }: PropsWithChildren) {

  useEffect(() => {
    // componentDidMount 逻辑
  }, [])

  useDidShow(() => {
    // componentDidShow 逻辑
  })

  useDidHide(() => {
    // componentDidHide 逻辑
  })

  // children 是将要会渲染的页面
  return children
}

export default App
