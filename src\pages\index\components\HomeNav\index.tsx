/**
 * HomeNav 首页导航组件
 * 
 * 2行4列的导航图标组件，包含8个导航项目
 */
import React, { useCallback } from 'react'
import { View, Image, Text } from '@tarojs/components'
import { navigateToPage, setClipboardData, setStorage } from '@/utils'
// 导入图标
import Icon1 from '@/assets/images/home-nav-icon/home_nav_1.png'
import Icon2 from '@/assets/images/home-nav-icon/home_nav_2.png'
import Icon3 from '@/assets/images/home-nav-icon/home_nav_3.png'
import Icon4 from '@/assets/images/home-nav-icon/home_nav_4.png'
import Icon5 from '@/assets/images/home-nav-icon/home_nav_5.png'
import Icon6 from '@/assets/images/home-nav-icon/home_nav_6.png'
import Icon7 from '@/assets/images/home-nav-icon/home_nav_7.png'
import Icon8 from '@/assets/images/home-nav-icon/home_nav_8.png'
import { HomeNavProps, NavItem } from './types'
import './index.scss'


// 默认导航项目配置
const defaultNavItems: NavItem[] = [
  {
    id: 1,
    iconSrc: Icon1,
    title: '黄金救援',
    linkUrl: '/subpackages/golden-rescue/index?type=1' // 暂时为空，后续手动填写
  },
  {
    id: 2,
    iconSrc: Icon2,
    title: '优配律师',
    linkUrl: '/subpackages/golden-rescue/index?type=2' // 暂时为空，后续手动填写
  },
  {
    id: 3,
    iconSrc: Icon3,
    title: '品牌律师',
    linkUrl: '/pages/lawyer/index' // 暂时为空，后续手动填写
  },
  {
    id: 4,
    iconSrc: Icon4,
    title: '金牌律师',
    linkUrl: '/pages/lawyer/index' // 暂时为空，后续手动填写
  },
  {
    id: 5,
    iconSrc: Icon5,
    title: '我的案件',
    linkUrl: '/subpackages/my-cases/index' // 暂时为空，后续手动填写
  },
  {
    id: 6,
    iconSrc: Icon6,
    title: '履约保证',
    linkUrl: '/subpackages/contract-management/index' // 暂时为空，后续手动填写
  },
  {
    id: 7,
    iconSrc: Icon7,
    title: '律师入驻',
    linkUrl: '/subpackages/lawyer-entry/index' // 暂时为空，后续手动填写
  },
  {
    id: 8,
    iconSrc: Icon8,
    title: '关于我们',
    linkUrl: '/subpackages/about-us/index' // 暂时为空，后续手动填写
  }
]

const HomeNav: React.FC<HomeNavProps> = ({
  navItems = defaultNavItems,
  className = '',
  onItemClick,
  disabled = false
}) => {
  // 处理导航项点击
  const handleItemClick = useCallback(async (item: NavItem, index: number) => {
    if (disabled) return

    // 如果有自定义点击事件，优先执行
    if (onItemClick) {
      onItemClick(item, index)
      return
    }

    // 如果没有跳转链接，不执行任何操作
    if (!item.linkUrl) {
      console.log(`点击了 ${item.title}，但没有配置跳转链接`)
      return
    }

    // 执行页面跳转
    try {
      if (item.linkUrl.startsWith('http')) {
        // 外部链接，复制到剪贴板
        await setClipboardData(item.linkUrl)
      } else {
        // 金牌律师和品牌律师需要通过本地缓存传递参数
        if (item.title === '金牌律师' || item.title === '品牌律师') {
          await setStorage('lawyerFilter', {
            level: item.title === '金牌律师' ? 4 : 3
          })
        }
        // 使用全局导航函数
        await navigateToPage(item.linkUrl)
      }
    } catch (error) {
      console.error('导航跳转失败:', error)
    }
  }, [onItemClick, disabled])

  return (
    <View className={`home-nav ${className} ${disabled ? 'home-nav--disabled' : ''}`}>
      <View className='home-nav__container'>
        {navItems.map((item, index) => (
          <View
            key={item.id}
            className={`home-nav__item ${!item.linkUrl ? 'home-nav__item--no-link' : ''}`}
            onClick={() => handleItemClick(item, index)}
          >
            {/* 图标 */}
            <View className='home-nav__icon-wrapper'>
              <Image
                className='home-nav__icon'
                src={item.iconSrc}
                mode='aspectFit'
              />
            </View>

            {/* 标题 */}
            <Text className='home-nav__title'>
              {item.title}
            </Text>
          </View>
        ))}
      </View>
    </View>
  )
}

export default HomeNav
