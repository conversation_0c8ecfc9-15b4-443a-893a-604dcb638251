/**
 * 律师动态Tab内容组件
 *
 * 包含动态搜索、分类筛选和动态列表三个部分
 */
import React, { useState, useEffect, useCallback, useRef } from 'react'
import { View } from '@tarojs/components'
import { dynamicsApi } from '@/utils/request/apis'
import { navigateToPage } from '@/utils'
import { DynamicsHeader, DynamicsCategories, DynamicsList } from './components'
import './index.scss'
// 组件属性
export interface LawyerDynamicsTabProps {
  // 律师ID
  lawyerId: number
  // 是否显示加载状态
  loading?: boolean
  // 自定义样式类名
  className?: string
}

const LawyerDynamicsTab: React.FC<LawyerDynamicsTabProps> = ({
  lawyerId,
  loading = false,
  className = ''
}) => {
  // 搜索关键词
  const [searchKeyword, setSearchKeyword] = useState('')
  // 动态分类列表
  const [categories, setCategories] = useState<DynamicsAPI.DynamicsCategoryInfo[]>([])
  // 选中的分类ID
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null)
  // 分类是否展开
  const [isCategoryExpanded, setIsCategoryExpanded] = useState(false)
  // 动态列表
  const [dynamics, setDynamics] = useState<DynamicsAPI.DynamicsListInfo[]>([])
  // 加载状态
  const [isLoading, setIsLoading] = useState(false)
  // 防抖定时器
  const debounceTimerRef = useRef<number | null>(null)

  // 格式化时间
  const formatTime = (timeStr: string): string => {
    const date = new Date(timeStr)
    return `${date.getMonth() + 1}-${String(date.getDate()).padStart(2, '0')}`
  }

  // 加载动态分类
  const loadCategories = async () => {
    try {
      const response = await dynamicsApi.getDynamicsCategoryList()
      if (response.success && response.data) {
        const allCategory = { id: 0, name: '全部' }
        setCategories([allCategory, ...response.data.list])
        setSelectedCategoryId(0) // 默认选中全部
      }
    } catch (error) {
      console.error('加载分类失败:', error)
      // 使用模拟数据
      const mockCategories = [
        { id: 0, name: '全部' },
        { id: 1, name: '法律资讯' },
        { id: 2, name: '案例分享' },
        { id: 3, name: '专业见解' },
        { id: 4, name: '行业动态' },
        { id: 5, name: '法规解读' },
        { id: 6, name: '实务指导' }
      ]
      setCategories(mockCategories)
      setSelectedCategoryId(0)
    }
  }

  // 加载动态列表
  const loadDynamics = useCallback(async () => {
    try {
      setIsLoading(true)
      const params: DynamicsAPI.GetDynamicsListRequest = {
        lawyerId,
        page: 1,
        pageSize: 5,
        ...(selectedCategoryId && selectedCategoryId !== 0 && { categoryId: selectedCategoryId }),
        ...(searchKeyword && { keyword: searchKeyword })
      }

      const response = await dynamicsApi.getDynamicsList(params)
      if (response.success && response.data) {
        setDynamics(response.data.list)
      }
    } catch (error) {
      console.error('加载动态列表失败:', error)
      // 使用模拟数据
      const mockDynamics: DynamicsAPI.DynamicsListInfo[] = [
        {
          id: 1,
          title: '最新法律法规解读：民法典实施后的重要变化',
          categoryId: 1,
          categoryName: '法律资讯',
          viewCount: 1250,
          createdAt: '2024-01-15T10:30:00Z'
        },
        {
          id: 2,
          title: '成功案例分享：某企业合同纠纷案件处理经验',
          categoryId: 2,
          categoryName: '案例分享',
          viewCount: 890,
          createdAt: '2024-01-12T14:20:00Z'
        },
        {
          id: 3,
          title: '专业见解：如何有效防范企业法律风险',
          categoryId: 3,
          categoryName: '专业见解',
          viewCount: 2100,
          createdAt: '2024-01-10T09:15:00Z'
        }
      ]
      setDynamics(mockDynamics)
    } finally {
      setIsLoading(false)
    }
  }, [lawyerId, selectedCategoryId, searchKeyword])

  // 防抖加载动态列表
  const debouncedLoadDynamics = useCallback(() => {
    // 清除之前的定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current)
    }

    // 设置新的定时器
    debounceTimerRef.current = window.setTimeout(() => {
      loadDynamics()
    }, 300) // 300ms 防抖延迟
  }, [loadDynamics])

  // 处理搜索输入
  const handleSearchInput = (e: any) => {
    setSearchKeyword(e.detail.value)
  }

  // 处理分类选择
  const handleCategorySelect = (categoryId: number) => {
    setSelectedCategoryId(categoryId)
  }

  // 切换分类展开状态
  const toggleCategoryExpanded = () => {
    setIsCategoryExpanded(!isCategoryExpanded)
  }

  // 处理动态点击
  const handleDynamicsClick = (dynamicsInfo: DynamicsAPI.DynamicsListInfo) => {
    navigateToPage(`/subpackages/detail/dynamics/index?id=${dynamicsInfo.id}`)
  }

  // 组件挂载时加载数据
  useEffect(() => {
    loadCategories()
  }, [])

  // 当分类或搜索条件变化时重新加载动态
  useEffect(() => {
    if (categories.length > 0) {
      // 对于分类选择，立即加载（用户主动选择）
      // 对于搜索，使用防抖加载（避免频繁输入时的闪烁）
      if (searchKeyword) {
        debouncedLoadDynamics()
      } else {
        loadDynamics()
      }
    }
  }, [selectedCategoryId, searchKeyword, lawyerId, categories.length, loadDynamics, debouncedLoadDynamics])

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
    }
  }, [])

  return (
      <View className={`lawyer-dynamics-tab ${className}`}>
        {/* 标题和搜索框 */}
        <DynamicsHeader
          searchKeyword={searchKeyword}
          onSearchInput={handleSearchInput}
        />

        {/* 动态类型选择 */}
        <DynamicsCategories
          categories={categories}
          selectedCategoryId={selectedCategoryId}
          isExpanded={isCategoryExpanded}
          onCategorySelect={handleCategorySelect}
          onToggleExpanded={toggleCategoryExpanded}
        />

        {/* 动态列表 */}
        <DynamicsList
          dynamics={dynamics}
          isLoading={isLoading}
          onDynamicsClick={handleDynamicsClick}
          formatTime={formatTime}
        />
      </View>
  )
}

export default LawyerDynamicsTab
