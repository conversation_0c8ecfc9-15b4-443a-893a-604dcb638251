/**
 * 律师文章列表组件
 * 
 * 支持滚动加载，展示律师的文章列表
 */
import React, { useState, useEffect } from 'react'
import { View, Text, Image, ScrollView } from '@tarojs/components'
import { articleApi } from '@/utils/request/apis/article'
import { formatTime } from '@/utils'
// 导入图标
import clockIcon from '@/assets/images/common-icon/clock.png'
import eyeIcon from '@/assets/images/common-icon/eye.png'
import likeOutlineIcon from '@/assets/images/common-icon/like_outlone_g.png'
import './index.scss'

// 组件属性
export interface LawyerArticleListProps {
  // 律师ID
  lawyerId: number
  // 自定义样式类名
  className?: string
  // 文章点击回调
  onArticleClick?: (article: ArticleAPI.ArticleListInfo) => void
}

const LawyerArticleList: React.FC<LawyerArticleListProps> = ({
  lawyerId,
  className = '',
  onArticleClick
}) => {
  const [articleList, setArticleList] = useState<ArticleAPI.ArticleListInfo[]>([])
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(1)
  const pageSize = 20

  // 加载文章列表
  const loadArticleList = async (pageNum: number = 1, isRefresh: boolean = false) => {
    if (loading) return

    try {
      setLoading(true)
      const response = await articleApi.getLawyerArticleList(lawyerId, {
        page: pageNum,
        pageSize
      })

      if (response.success && response.data) {
        const newList = response.data.list || []
        
        if (isRefresh) {
          setArticleList(newList)
        } else {
          setArticleList(prev => [...prev, ...newList])
        }

        // 判断是否还有更多数据
        setHasMore(newList.length === pageSize)
        setPage(pageNum)
      }
    } catch (error) {
      console.error('加载文章列表失败:', error)
      
      // 模拟数据
      const mockArticles: ArticleAPI.ArticleListInfo[] = Array.from({ length: pageSize }, (_, index) => ({
        id: pageNum * pageSize + index + 1,
        title: `律师文章标题${pageNum * pageSize + index + 1}：这是一个很长的文章标题，用来测试两行省略效果`,
        categoryId: 1,
        categoryName: '法律咨询',
        likeCount: Math.floor(Math.random() * 100),
        viewCount: Math.floor(Math.random() * 1000),
        favoriteCount: Math.floor(Math.random() * 50),
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
      }))

      if (isRefresh) {
        setArticleList(mockArticles)
      } else {
        setArticleList(prev => [...prev, ...mockArticles])
      }
      setHasMore(pageNum < 3) // 模拟3页数据
      setPage(pageNum)
    } finally {
      setLoading(false)
    }
  }

  // 处理滚动到底部
  const handleScrollToLower = () => {
    if (hasMore && !loading) {
      loadArticleList(page + 1, false)
    }
  }

  // 处理文章点击
  const handleArticleClick = (article: ArticleAPI.ArticleListInfo) => {
    if (onArticleClick) {
      onArticleClick(article)
    } else {
      console.log('点击文章:', article.title)
      // 默认跳转到文章详情页
      // navigateToPage(`/subpackages/detail/article/index?articleId=${article.id}`)
    }
  }

  // 格式化数字显示
  const formatNumber = (num: number): string => {
    if (num >= 10000) {
      return `${(num / 10000).toFixed(1)}万`
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`
    }
    return num.toString()
  }

  // 初始化加载
  useEffect(() => {
    if (lawyerId) {
      loadArticleList(1, true)
    }
  }, [lawyerId])

  return (
    <View className={`lawyer-article-list ${className}`}>
      <ScrollView
        className='lawyer-article-list__scroll'
        scrollY
        onScrollToLower={handleScrollToLower}
        lowerThreshold={100}
      >
        {articleList.map((article, index) => (
          <View
            key={article.id}
            className='lawyer-article-list__item'
            onClick={() => handleArticleClick(article)}
          >
            {/* 文章标题 */}
            <Text className='lawyer-article-list__title'>{article.title}</Text>
            
            {/* 文章内容预览 */}
            <Text className='lawyer-article-list__content'>
              这是文章内容的预览部分，通常会显示文章的前几行内容，用来让用户了解文章的大概内容。这里是模拟的内容预览文本。
            </Text>

            {/* 文章信息 */}
            <View className='lawyer-article-list__info'>
              <View className='lawyer-article-list__info-item'>
                <Image className='lawyer-article-list__info-icon' src={clockIcon} mode='aspectFit' />
                <Text className='lawyer-article-list__info-text'>{formatTime(article.createdAt)}</Text>
              </View>
              <View className='lawyer-article-list__info-item'>
                <Image className='lawyer-article-list__info-icon' src={eyeIcon} mode='aspectFit' />
                <Text className='lawyer-article-list__info-text'>{formatNumber(article.viewCount)}</Text>
              </View>
              <View className='lawyer-article-list__info-item'>
                <Image className='lawyer-article-list__info-icon' src={likeOutlineIcon} mode='aspectFit' />
                <Text className='lawyer-article-list__info-text'>{formatNumber(article.likeCount)}</Text>
              </View>
            </View>

            {/* 分割线 */}
            {index < articleList.length - 1 && (
              <View className='lawyer-article-list__divider' />
            )}
          </View>
        ))}

        {/* 加载状态 */}
        {loading && (
          <View className='lawyer-article-list__loading'>
            <Text className='lawyer-article-list__loading-text'>加载中...</Text>
          </View>
        )}

        {/* 没有更多数据 */}
        {!hasMore && articleList.length > 0 && (
          <View className='lawyer-article-list__no-more'>
            <Text className='lawyer-article-list__no-more-text'>没有更多文章了</Text>
          </View>
        )}

        {/* 空状态 */}
        {!loading && articleList.length === 0 && (
          <View className='lawyer-article-list__empty'>
            <Text className='lawyer-article-list__empty-text'>暂无文章</Text>
          </View>
        )}
      </ScrollView>
    </View>
  )
}

export default LawyerArticleList
