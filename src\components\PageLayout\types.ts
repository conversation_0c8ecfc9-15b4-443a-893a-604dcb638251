/**
 * PageLayout 相关类型定义
 */
import { ReactNode } from 'react'

// 页面布局属性接口
export interface PageLayoutProps {
  // 页面标题
  title?: string
  // 是否显示导航栏
  showNavBar?: boolean
  // 是否显示返回按钮
  showBack?: boolean
  // 导航栏背景色
  navBarBgColor?: string
  // 导航栏文字颜色
  navBarTextColor?: string
  // 是否显示导航栏底部边框
  showNavBorder?: boolean
  // 页面背景色
  backgroundColor?: string
  // 内容区域样式类名
  contentClassName?: string
  // 是否启用下拉刷新
  enablePullRefresh?: boolean
  // 下拉刷新回调
  onPullRefresh?: () => void
  // 是否启用触底加载
  enableReachBottom?: boolean
  // 触底加载回调
  onReachBottom?: () => void
  // 页面内容
  children: ReactNode
  // 自定义导航栏左侧内容
  navBarLeft?: ReactNode
  // 自定义导航栏右侧内容
  navBarRight?: ReactNode
  // 是否全屏显示（隐藏导航栏）
  fullScreen?: boolean
  // 是否安全区域适配
  safeArea?: boolean
}

// 页面状态枚举
export enum PageState {
  LOADING = 'loading',
  SUCCESS = 'success',
  EMPTY = 'empty',
  ERROR = 'error'
}

// 加载状态属性接口
export interface PageLoadingProps {
  text?: string
}

// 空状态属性接口
export interface PageEmptyProps {
  icon?: string
  text?: string
  description?: string
  actionText?: string
  onAction?: () => void
}

// 错误状态属性接口
export interface PageErrorProps {
  icon?: string
  text?: string
  description?: string
  retryText?: string
  onRetry?: () => void
}

// 加载更多属性接口
export interface PageLoadMoreProps {
  loading?: boolean
  hasMore?: boolean
  loadingText?: string
  noMoreText?: string
  onLoadMore?: () => void
}

// 页面内容属性接口
export interface PageContentProps {
  children: ReactNode
  className?: string
}

// 页面卡片属性接口
export interface PageCardProps {
  children: ReactNode
  className?: string
  title?: string
  extra?: ReactNode
}

// 页面列表属性接口
export interface PageListProps {
  children: ReactNode
  className?: string
}

// 分页数据接口
export interface PaginationData<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}

// 页面配置接口
export interface PageConfig {
  title: string
  showNavBar?: boolean
  showBack?: boolean
  backgroundColor?: string
  enablePullRefresh?: boolean
  enableReachBottom?: boolean
  safeArea?: boolean
}
