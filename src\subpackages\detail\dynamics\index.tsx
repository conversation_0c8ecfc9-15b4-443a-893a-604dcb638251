/**
 * 律师动态详情页面
 */
import React, { useState, useEffect } from 'react'
import { View, Text, Button } from '@tarojs/components'
import { useRouter } from '@tarojs/taro'
import { dynamicsApi } from '@/utils/request/apis'
import { PageLayout } from '@/components'
import './index.scss'

interface DynamicsDetailInfo {
  id: number
  title: string
  content: string
  categoryId: number
  categoryName: string
  viewCount: number
  createdAt: string
}

const DynamicsDetail: React.FC = () => {
  const router = useRouter()
  const { id } = router.params
  
  const [dynamicsInfo, setDynamicsInfo] = useState<DynamicsDetailInfo | null>(null)

  // 格式化时间
  const formatTime = (timeStr: string): string => {
    const date = new Date(timeStr)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  }

  // 加载动态详情
  const loadDynamicsDetail = async () => {
    try {
      if (id) {
        const response = await dynamicsApi.getDynamicsDetail(Number(id))
        if (response.success && response.data) {
          setDynamicsInfo(response.data.detail)
        }
      }
    } catch (error) {
      // 模拟数据
      const mockData: DynamicsDetailInfo = {
        id: Number(id) || 1,
        title: '律师行业发展趋势分析：数字化转型的机遇与挑战',
        content: `
随着科技的快速发展，律师行业正面临着前所未有的变革。数字化转型不仅改变了法律服务的提供方式，也为律师事务所带来了新的机遇和挑战。

一、数字化转型的主要趋势

1. 人工智能在法律服务中的应用
人工智能技术正在逐步应用于合同审查、法律研究、案例分析等领域，大大提高了工作效率。

2. 在线法律服务平台的兴起
越来越多的法律服务开始向线上转移，为客户提供更便捷的服务体验。

3. 数据分析在法律决策中的作用
通过大数据分析，律师可以更好地预测案件结果，制定更有效的诉讼策略。

二、面临的机遇

1. 提高服务效率
数字化工具可以帮助律师更快速地处理常规性工作，将更多时间投入到高价值的法律分析中。

2. 扩大服务范围
通过在线平台，律师可以为更广泛的客户群体提供服务，突破地域限制。

3. 降低运营成本
数字化办公可以减少纸质文档的使用，降低办公成本。

三、面临的挑战

1. 技术投入成本
数字化转型需要大量的技术投入，对于中小型律师事务所来说可能是一个负担。

2. 数据安全问题
法律服务涉及大量敏感信息，如何保障数据安全是一个重要挑战。

3. 人才培养
需要培养既懂法律又懂技术的复合型人才。

四、应对策略

1. 循序渐进地推进数字化
根据自身实际情况，制定合理的数字化转型计划。

2. 加强数据安全保护
建立完善的数据安全管理制度，确保客户信息安全。

3. 投资人才培养
加强员工的技术培训，提升数字化应用能力。

总结：
数字化转型是律师行业发展的必然趋势，只有积极拥抱变化，才能在激烈的市场竞争中立于不败之地。
        `,
        categoryId: 1,
        categoryName: '行业动态',
        viewCount: 856,
        createdAt: '2025-06-22 09:15:00'
      }
      setDynamicsInfo(mockData)
      console.error('加载动态详情失败:', error)
    }
  }

  useEffect(() => {
    loadDynamicsDetail()
  }, [id])

  return (
    <PageLayout
      title="动态详情"
    >
      {dynamicsInfo && (
        <View className="dynamics-detail__content">
          {/* 动态标题 */}
          <Text className="dynamics-title">{dynamicsInfo.title}</Text>
          
          {/* 动态信息 */}
          <View className="dynamics-meta">
            <View className="dynamics-meta__row">
              <Text className="dynamics-meta__label">分类：</Text>
              <Text className="dynamics-meta__value">{dynamicsInfo.categoryName}</Text>
            </View>
            <View className="dynamics-meta__row">
              <Text className="dynamics-meta__label">发布时间：</Text>
              <Text className="dynamics-meta__value">
                {formatTime(dynamicsInfo.createdAt)}
              </Text>
            </View>
            <View className="dynamics-meta__row">
              <Text className="dynamics-meta__label">浏览量：</Text>
              <Text className="dynamics-meta__value">{dynamicsInfo.viewCount}</Text>
            </View>
          </View>

          {/* 动态内容 */}
          <View className="dynamics-content">
            <Text className="dynamics-content__title">动态详情</Text>
            <Text className="dynamics-content__text">
              {dynamicsInfo.content}
            </Text>
          </View>

          {/* 操作按钮 */}
          <View className="dynamics-actions">
            <Button className="dynamics-actions__btn" type="primary">
              关注律师
            </Button>
            <Button className="dynamics-actions__btn" plain>
              分享动态
            </Button>
          </View>
        </View>
      )}
    </PageLayout>
  )
}

export default DynamicsDetail
