# 案例发布/编辑页面

律师案例发布和编辑功能页面，支持创建新案例和编辑已有案例。

## 功能特性

- ✅ **案例发布**：创建新的律师案例
- ✅ **案例编辑**：编辑已有的案例内容
- ✅ **分类选择**：通过接口获取案例分类列表
- ✅ **标题输入**：案例标题限制100字以内
- ✅ **内容输入**：案例内容限制5000字以内
- ✅ **实时字数统计**：显示当前字数和剩余字数
- ✅ **表单验证**：标题、内容和分类必填验证
- ✅ **加载状态**：提交时显示加载状态
- ✅ **错误处理**：网络错误和业务错误处理
- ✅ **响应式设计**：适配不同屏幕尺寸
- ✅ **深色主题**：支持深色主题适配
- ✅ **Flex布局**：页面不滚动，中间区域自适应

## 页面路由

```
/subpackages/publish-case/index
```

## 路由参数

| 参数 | 类型 | 说明 |
|------|------|------|
| id | string | 案例ID，编辑模式时传入 |

## 使用方法

### 发布新案例

```tsx
// 跳转到发布页面
Taro.navigateTo({ 
  url: '/subpackages/publish-case/index' 
})
```

### 编辑已有案例

```tsx
// 跳转到编辑页面
Taro.navigateTo({ 
  url: '/subpackages/publish-case/index?id=123' 
})
```

## 接口依赖

### 获取分类列表
- **接口**：`caseApi.getCaseCategoryList()`
- **说明**：获取案例分类列表，用于分类选择器

### 获取案例详情（编辑模式）
- **接口**：`caseApi.getCaseDetail(caseId)`
- **说明**：编辑模式下获取案例详情，填充表单

### 创建案例
- **接口**：`caseApi.createCase(params)`
- **参数**：`{ title: string, categoryId: number, content: string }`
- **说明**：发布新案例

### 更新案例
- **接口**：`caseApi.updateCase(params)`
- **参数**：`{ id: number, title: string, categoryId: number, content: string }`
- **说明**：更新已有案例

## 数据结构

### 表单数据
```typescript
interface FormData {
  title: string      // 案例标题
  categoryId: number // 分类ID
  content: string    // 案例内容
}
```

### 分类数据
```typescript
interface CaseCategoryInfo {
  id: number    // 分类ID
  name: string  // 分类名称
}
```

## 样式设计

### 配色方案
- **主色调**：#BD8A4F（金棕色）
- **背景色**：#f8f9fa（浅灰色）
- **卡片背景**：#ffffff（白色）
- **文字颜色**：#333333（深灰色）
- **辅助文字**：#999999（中灰色）

### 组件样式
- **表单区域**：白色卡片，圆角20rpx，阴影效果
- **输入框**：浅灰背景，聚焦时变白色，边框高亮
- **提交按钮**：渐变背景，圆角48rpx，阴影效果
- **分类选择器**：类似输入框样式，右侧箭头指示

## 布局设计

### Flex 三段式布局
1. **内容区域**：`flex: 1` 占满中间空间
   - 分类选择：`flex-shrink: 0` 固定高度
   - 标题输入：`flex-shrink: 0` 固定高度
   - 内容输入：`flex: 1` 占满剩余空间
2. **提交区域**：`flex-shrink: 0` 固定在底部

### 字数限制
- **标题**：最多100字
- **内容**：最多5000字
- **实时统计**：显示当前字数/最大字数

## 交互设计

### 用户操作流程
1. **选择分类**：点击分类选择器，选择案例分类
2. **输入标题**：在标题框中输入案例标题（最多100字）
3. **输入内容**：在内容框中输入案例详情（最多5000字）
4. **实时反馈**：显示字数统计，超出限制时禁止输入
5. **提交发布**：点击发布按钮，显示加载状态
6. **结果反馈**：成功后显示提示并返回上一页

### 编辑模式
1. **加载数据**：根据ID获取案例详情
2. **填充表单**：自动填充分类、标题和内容
3. **修改内容**：用户可修改所有字段
4. **更新保存**：点击更新按钮保存修改

## 表单验证

### 必填验证
- 标题为空：提示"请输入案例标题"
- 内容为空：提示"请输入案例内容"
- 未选择分类：提示"请选择案例分类"

### 字数验证
- 标题超长：自动截断到100字
- 内容超长：自动截断到5000字

## 错误处理

### 网络错误
- 分类加载失败：使用默认分类数据
- 案例详情加载失败：显示错误提示
- 提交失败：显示具体错误信息

### 降级处理
- 分类接口失败时使用模拟数据：民事案例、刑事案例、商事案例、行政案例

## 响应式适配

### 移动端优化
- 减小内边距和字体大小
- 调整输入框高度和圆角
- 优化按钮尺寸

### 深色主题
- 背景色调整为深色系
- 文字颜色反转
- 保持良好的对比度

## 技术特性

- **TypeScript**：完整的类型定义
- **Flex布局**：页面不滚动，自适应布局
- **Button组件**：使用原生Button组件
- **表单验证**：前端验证配合后端验证
- **状态管理**：React Hooks
- **错误边界**：网络错误处理

## 开发说明

### 本地开发
页面支持模拟数据，在网络请求失败时会使用默认分类数据，便于本地开发和测试。

### 接口对接
确保后端接口返回格式符合类型定义，特别注意分类列表和案例详情的数据结构。

### 样式定制
页面样式采用SCSS编写，支持主题定制和响应式适配，与全局样式风格保持一致。

## 注意事项

1. **字数限制**：标题100字，内容5000字
2. **分类必选**：必须选择一个案例分类
3. **网络依赖**：需要网络连接获取分类和提交数据
4. **状态管理**：提交过程中禁用表单防止重复提交
5. **数据验证**：前端验证配合后端验证确保数据完整性
6. **内容格式**：内容字段支持HTML/富文本格式
