/**
 * 律师动态Tab内容组件样式
 */

.lawyer-dynamics-tab {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

// 标题和搜索框区域
.dynamics-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;

  &__title {
    font-weight: 600;
    font-size: 32rpx;
    color: #000000;
    line-height: 36rpx;
  }

  &__search {
    display: flex;
    align-items: center;
    width: 400rpx;
    height: 66rpx;
    background: #F7F7F7;
    border-radius: 33rpx;
    padding: 0 20rpx;
    box-sizing: border-box;
  }

  &__search-icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 12rpx;
    flex-shrink: 0;
  }

  &__search-input {
    flex: 1;
    font-size: 28rpx;
    color: #333333;
    background: transparent;
    border: none;
    outline: none;
  }

  &__search-placeholder {
    color: #999999;
    font-size: 28rpx;
  }
}

// 动态分类选择区域
.dynamics-categories {
  margin-bottom: 30rpx;

  &__list {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    margin-bottom: 20rpx;
  }

  &__item {
    padding: 10rpx 20rpx;
    background: #FFFFFF;
    border: 2rpx solid #E6E9ED;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &--active {
      background: #BD8A4F;
      border-color: #BD8A4F;

      .dynamics-categories__item-text {
        color: #FFFFFF;
      }
    }

    &:not(&--active):active {
      background: #F5F5F5;
    }
  }

  &__item-text {
    font-size: 28rpx;
    color: #666666;
    font-weight: 500;
    transition: color 0.3s ease;
  }

  &__toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 10rpx;
  }

  &__toggle-icon {
    width: 32rpx;
    height: 32rpx;
    transition: transform 0.3s ease;

    &--expanded {
      transform: rotate(180deg);
    }
  }
}

// 动态列表区域
.dynamics-list {

  &__loading,
  &__empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200rpx;
    color: #999999;
    font-size: 28rpx;
  }

  // 内容区域
  &__content {
    position: relative;
    transition: opacity 0.3s ease;

    // 加载中状态的内容区域
    &--loading {
      opacity: 0.7;
    }
  }

  // 加载中遮罩层
  &__loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(2rpx);
    z-index: 10;
  }

  // 加载中文本
  &__loading-text {
    color: #BD8A4F;
    font-size: 24rpx;
    font-weight: 500;
    padding: 10rpx 20rpx;
    background: rgba(189, 138, 79, 0.1);
    border-radius: 20rpx;
  }
}

// 动态卡片
.dynamics-item {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #E0DEE5;
  cursor: pointer;
  transition: background-color 0.2s ease;
  min-height: 150rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #F8F9FA;
  }

  &__title {
    font-weight: 600;
    font-size: 28rpx;
    color: #000000;
    line-height: 36rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    margin-bottom: 20rpx;
  }

  &__meta {
    display: flex;
    align-items: center;
  }

  &__category {
    font-size: 24rpx;
    color: #BD8A4F;
    font-weight: 500;
    margin-right: 20rpx;
  }

  &__info-icon {
    width: 24rpx;
    height: 24rpx;
    margin-right: 8rpx;
  }

  &__info-text {
    font-size: 24rpx;
    color: #999999;
    margin-right: 20rpx;
  }
}