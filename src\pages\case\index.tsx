import React, { useEffect, useState } from 'react'
import { View, Input, Image, Text } from '@tarojs/components'
import Taro from '@tarojs/taro'
import PageLayout, { PageContent } from '@/components/PageLayout'
import { caseApi } from '@/utils/request/apis'
import './index.scss'
import CaseList from '@/components/CaseList'
import SearchIcon from '@/assets/images/common-icon/search.png'

const Case: React.FC = () => {
  // 搜索关键词
  const [searchKeyword, setSearchKeyword] = useState('')
  // 案例分类列表
  const [caseCategories, setCaseCategories] = useState<CaseAPI.CaseCategoryInfo[]>([])
  // 选中的分类ID
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | undefined>(undefined)
  // 分类加载状态
  const [categoriesLoading, setCategoriesLoading] = useState(false)
  // 筛选参数
  const [filterParams, setFilterParams] = useState<CaseAPI.GetCaseListRequest>({})

  // 获取案例分类数据
  const loadCaseCategories = async () => {
    try {
      setCategoriesLoading(true)

      const response = await caseApi.getCaseCategoryList()
      if (response.success && response.data) {
        setCaseCategories(response.data.list)
      }
    } catch (error) {
      console.error('获取案例分类失败，使用mock数据:', error)

      // 使用mock数据
      const mockCategories: CaseAPI.CaseCategoryInfo[] = [
        { id: 1, name: '劳动纠纷' },
        { id: 2, name: '房产纠纷' },
        { id: 3, name: '交通事故' },
        { id: 4, name: '婚姻家庭' },
        { id: 5, name: '公司法务' },
        { id: 6, name: '知识产权' },
        { id: 7, name: '刑事辩护' },
        { id: 8, name: '合同纠纷' }
      ]
      setCaseCategories(mockCategories)
    } finally {
      setCategoriesLoading(false)
    }
  }

  // 处理搜索输入
  const handleSearchInput = (e: any) => {
    const value = e.detail.value
    setSearchKeyword(value)
    updateFilterParams({ title: value })
  }

  // 处理分类选择
  const handleCategorySelect = (categoryId?: number) => {
    setSelectedCategoryId(categoryId)
    updateFilterParams({ categoryId })
  }

  // 更新筛选参数
  const updateFilterParams = (params: Partial<CaseAPI.GetCaseListRequest>) => {
    setFilterParams(prev => ({ ...prev, ...params }))
  }

  // 页面生命周期
  useEffect(() => {
    console.log('案例页面加载完成')
    loadCaseCategories()

    return () => {
      console.log('案例页面卸载')
    }
  }, [])

  // 页面显示时触发
  Taro.useDidShow(() => {
    console.log('案例页面显示')
  })

  // 页面隐藏时触发
  Taro.useDidHide(() => {
    console.log('案例页面隐藏')
  })

  return (
    <PageLayout
      title="案例"
      showBack={false}
    >
      <PageContent padded='b'>
        <View className="case-content">
          <View className="case-content__header">
            {/* 搜索框 */}
            <View className="search-container">
              <View className="search-box">
                <Image src={SearchIcon} className="search-icon" mode="aspectFit" />
                <Input
                  className="search-input"
                  placeholder="搜索案例"
                  value={searchKeyword}
                  onInput={handleSearchInput}
                />
              </View>
            </View>

            {/* 案例类型选择 */}
            <View className="category-container">
              <View
                className={`category-item ${selectedCategoryId === undefined ? 'category-item--active' : ''}`}
                onClick={() => handleCategorySelect(undefined)}
              >
                <Text className="category-text">全部案例</Text>
              </View>
              {categoriesLoading ? (
                <View className="category-loading">
                  <Text className="category-loading-text">加载分类中...</Text>
                </View>
              ) : (
                caseCategories.map((category) => (
                  <View
                    key={category.id}
                    className={`category-item ${selectedCategoryId === category.id ? 'category-item--active' : ''}`}
                    onClick={() => handleCategorySelect(category.id)}
                  >
                    <Text className="category-text">{category.name}</Text>
                  </View>
                ))
              )}
            </View>
          </View>
          <View className="case-content__list">
            <CaseList filterParams={filterParams} />
          </View>
        </View>
      </PageContent>
    </PageLayout>
  )
}

export default Case
