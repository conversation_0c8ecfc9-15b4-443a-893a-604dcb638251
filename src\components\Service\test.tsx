/**
 * Service 组件测试文件
 */
import React, { useState } from 'react'
import { View, Text, Button } from '@tarojs/components'
import Service from './index'

const ServiceTest: React.FC = () => {
  const [visible, setVisible] = useState(true)

  return (
    <View style={{ padding: '40rpx', minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
      <Text style={{ fontSize: '36rpx', fontWeight: 'bold', marginBottom: '30rpx' }}>
        Service 客服组件测试
      </Text>
      
      <View style={{ 
        backgroundColor: '#fff', 
        padding: '30rpx', 
        borderRadius: '20rpx',
        marginBottom: '30rpx'
      }}>
        <Text style={{ fontSize: '28rpx', marginBottom: '20rpx' }}>
          当前状态: {visible ? '显示' : '隐藏'}
        </Text>
        
        <View style={{ display: 'flex', gap: '20rpx' }}>
          <Button
            size="mini"
            type={visible ? 'default' : 'primary'}
            onClick={() => setVisible(true)}
          >
            显示客服
          </Button>
          <Button
            size="mini"
            type={visible ? 'primary' : 'default'}
            onClick={() => setVisible(false)}
          >
            隐藏客服
          </Button>
        </View>
      </View>

      <View style={{ 
        backgroundColor: '#fff', 
        padding: '30rpx', 
        borderRadius: '20rpx',
        marginBottom: '30rpx'
      }}>
        <Text style={{ fontSize: '28rpx', marginBottom: '15rpx', fontWeight: '600' }}>
          功能说明:
        </Text>
        <Text style={{ fontSize: '24rpx', color: '#666', lineHeight: '32rpx' }}>
          • 客服组件固定在右下角{'\n'}
          • 点击可打开微信客服对话{'\n'}
          • 支持显示/隐藏控制{'\n'}
          • 包含点击动画效果{'\n'}
          • 客服不可用时提供电话备用方案
        </Text>
      </View>

      <View style={{ 
        backgroundColor: '#fff', 
        padding: '30rpx', 
        borderRadius: '20rpx'
      }}>
        <Text style={{ fontSize: '28rpx', marginBottom: '15rpx', fontWeight: '600' }}>
          样式规格:
        </Text>
        <Text style={{ fontSize: '22rpx', color: '#999', lineHeight: '28rpx', fontFamily: 'monospace' }}>
          位置: 距右边25rpx，距底边65rpx{'\n'}
          尺寸: 88rpx × 88rpx 圆形{'\n'}
          背景: #fff 白色{'\n'}
          图标: 40rpx × 40rpx{'\n'}
          文字: 16rpx, font-weight: 600{'\n'}
          间距: 图标与文字间距5rpx
        </Text>
      </View>

      {/* 客服组件 */}
      <Service visible={visible} />
    </View>
  )
}

export default ServiceTest
