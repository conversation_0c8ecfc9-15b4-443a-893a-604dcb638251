export default defineAppConfig({
  pages: [
    'pages/index/index',
    'pages/lawyer/index',
    'pages/case/index',
    'pages/mine/index'
  ],
  window: {
    navigationStyle: "custom",
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '律师咨询平台',
    navigationBarTextStyle: 'black'
  },
  tabBar: {
    color: '#999999',
    selectedColor: '#BD8A4F',
    backgroundColor: '#000000',
    borderStyle: 'white',
    list: [
      {
        pagePath: 'pages/index/index',
        text: '首页',
        iconPath: 'assets/images/tabbar-icon/Home.png',
        selectedIconPath: 'assets/images/tabbar-icon/Home_active.png'
      },
      {
        pagePath: 'pages/lawyer/index',
        text: '找律师',
        iconPath: 'assets/images/tabbar-icon/Search.png',
        selectedIconPath: 'assets/images/tabbar-icon/Search_active.png'
      },
      {
        pagePath: 'pages/case/index',
        text: '案例',
        iconPath: 'assets/images/tabbar-icon/Bookmark.png',
        selectedIconPath: 'assets/images/tabbar-icon/Bookmark_active.png'
      },
      {
        pagePath: 'pages/mine/index',
        text: '我的',
        iconPath: 'assets/images/tabbar-icon/User.png',
        selectedIconPath: 'assets/images/tabbar-icon/User_active.png'
      }
    ]
  },
  subPackages: [
    {
      root: 'subpackages/',
      pages: [
        'about-us/index',
        'contract-management/index',
        'detail/lawyer/index',
        'detail/article/index',
        'detail/case/index',
        'detail/dynamics/index',
        'excellent-cases/index',
        'golden-rescue/index',
        'lawyer-entry/index',
        'login/index',
        'my-cases/index',
        'my-case-detail/index',
        'publish-case/index',
        'publish-dynamic/index'
      ]
    }
  ],
  lazyCodeLoading: 'requiredComponents'
})
