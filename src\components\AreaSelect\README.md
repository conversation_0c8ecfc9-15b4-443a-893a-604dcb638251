# AreaSelect 省市区选择组件

基于 Taro Picker 组件的 `mode="region"` 封装的省市区选择器，使用微信小程序内置的省市区数据，无需手动加载接口。

## 功能特性

- ✅ **内置数据**：使用微信小程序内置省市区数据，无需API接口
- ✅ **三级选择**：支持省市区三级选择
- ✅ **自动适配**：自动处理直辖市等特殊情况
- ✅ **自定义触发器**：通过 children 传入任意展示内容
- ✅ **初始值设置**：支持设置默认选中的省市区
- ✅ **禁用状态**：支持禁用选择功能
- ✅ **TypeScript**：完整的类型定义支持

## 基础用法

```tsx
import React, { useState } from 'react'
import { View, Text } from '@tarojs/components'
import AreaSelect from '@/components/AreaSelect'

const MyComponent = () => {
  const [selectedArea, setSelectedArea] = useState('')

  const handleAreaChange = (result) => {
    console.log('选择结果:', result)
    setSelectedArea(result.areaString)
  }

  return (
    <AreaSelect onChange={handleAreaChange}>
      <View className="area-select-trigger">
        <Text className={`area-select-trigger__text ${!selectedArea ? 'area-select-trigger__text--placeholder' : ''}`}>
          {selectedArea || '请选择省市区'}
        </Text>
        <View className="area-select-trigger__arrow" />
      </View>
    </AreaSelect>
  )
}
```

## 表单中使用

```tsx
import React, { useState } from 'react'
import { View, Text } from '@tarojs/components'
import AreaSelect from '@/components/AreaSelect'

const FormExample = () => {
  const [formData, setFormData] = useState({
    area: '',
    areaIds: ''
  })

  const handleAreaChange = (result) => {
    setFormData(prev => ({
      ...prev,
      area: result.areaString,
      areaIds: result.areaIds
    }))
  }

  return (
    <View className="form">
      <View className="form-item">
        <View className="form-item__label form-item__label--required">
          所在地区
        </View>
        <View className="form-item__content">
          <AreaSelect 
            value={formData.areaIds}
            onChange={handleAreaChange}
          >
            <View className={`area-select-trigger ${formData.area ? 'area-select-trigger--selected' : ''}`}>
              <Text className={`area-select-trigger__text ${!formData.area ? 'area-select-trigger__text--placeholder' : ''}`}>
                {formData.area || '请选择省市区'}
              </Text>
              <View className="area-select-trigger__arrow" />
            </View>
          </AreaSelect>
        </View>
      </View>
    </View>
  )
}
```

## API 参数

### AreaSelectProps

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| children | ReactNode | - | 触发选择的展示内容 |
| onChange | (result: AreaSelectResult) => void | - | 选择完成回调 |
| value | string | - | 初始值（省市区ID，用'/'分隔） |
| disabled | boolean | false | 是否禁用 |
| className | string | '' | 自定义样式类名 |
| placeholder | string | '请选择省市区' | 占位符文本 |

### AreaSelectResult

| 字段 | 类型 | 说明 |
|------|------|------|
| areaString | string | 省市区字符串，用'/'分隔，如："广东省/深圳市/南山区" |
| areaIds | string | 省市区ID，用'/'分隔（region模式下为空字符串） |
| province | AreaItem | 省份信息 |
| city | AreaItem | 城市信息 |
| district | AreaItem? | 区县信息（可能为空） |

### AreaItem

| 字段 | 类型 | 说明 |
|------|------|------|
| id | string | 行政区ID |
| name | string | 行政区名称 |

## 数据来源

组件使用微信小程序内置的省市区数据，具有以下特点：

- **数据完整**：包含全国所有省市区数据
- **自动更新**：跟随微信小程序平台更新
- **无需接口**：不依赖外部API，减少网络请求
- **性能优秀**：数据本地化，选择响应快速

## 样式定制

组件提供了预设的触发器样式类 `.area-select-trigger`，你也可以完全自定义：

```scss
.custom-trigger {
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  background: #fff;
  
  &:active {
    background: #f5f5f5;
  }
}
```

## 注意事项

1. **数据来源**：使用微信小程序内置数据，无需外部接口
2. **初始值格式**：value 参数传入省市区名称，用'/'分隔，如 "广东省/深圳市/南山区"
3. **ID获取限制**：region模式无法获取具体的行政区ID，如需ID请使用API接口模式
4. **平台限制**：仅在微信小程序环境下可用

## 优势对比

**使用 region 模式的优势：**
- ✅ 无需网络请求，响应速度快
- ✅ 数据始终最新，跟随微信平台更新
- ✅ 代码简洁，无需处理异步加载逻辑
- ✅ 自动处理直辖市等特殊情况

**局限性：**
- ❌ 无法获取行政区ID
- ❌ 仅限微信小程序平台使用
