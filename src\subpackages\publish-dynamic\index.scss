/**
 * 动态发布/编辑页面样式
 */

.publish-dynamic {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8f9fa;
  overflow: hidden;

  // 加载状态
  .loading-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    .loading-text {
      font-size: 28rpx;
      color: #999999;
    }
  }

  // 内容区域
  .content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 32rpx;
    overflow: hidden;
  }

  // 表单区域
  .form-section {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    flex-shrink: 0;

    .form-label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 24rpx;

      .word-count {
        font-size: 24rpx;
        font-weight: 400;
        color: #999999;
      }
    }

    // 分类选择器
    .category-picker {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx 32rpx;
      background: #f8f9fa;
      border-radius: 16rpx;
      border: 2rpx solid #e9ecef;
      transition: all 0.2s ease;

      &:active {
        background: #e9ecef;
        transform: scale(0.98);
      }

      .picker-text {
        font-size: 30rpx;
        color: #333333;
        flex: 1;
      }

      .picker-arrow {
        font-size: 32rpx;
        color: #999999;
        font-weight: 300;
      }
    }

    // 内容输入框区域
    &.content-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-bottom: 0;
      min-height: 0;
    }

    // 内容输入框
    .content-textarea {
      width: 100%;
      flex: 1;
      min-height: 0;
      padding: 24rpx;
      background: #f8f9fa;
      border-radius: 16rpx;
      border: 2rpx solid #e9ecef;
      font-size: 30rpx;
      line-height: 1.6;
      color: #333333;
      box-sizing: border-box;
      transition: all 0.2s ease;
      resize: none;

      &:focus {
        border-color: #BD8A4F;
        background: #ffffff;
        box-shadow: 0 0 0 4rpx rgba(189, 138, 79, 0.1);
      }
    }
  }

  // 提交区域 - flex 布局底部
  .submit-section {
    flex-shrink: 0;
    background: #ffffff;
    padding: 24rpx 32rpx;
    padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
    border-top: 1rpx solid #e9ecef;

    .submit-btn {
      width: 100% !important;
      height: 96rpx !important;
      background: linear-gradient(135deg, #BD8A4F 0%, #d4a574 100%) !important;
      border-radius: 48rpx !important;
      border: none !important;
      font-size: 32rpx !important;
      font-weight: 600 !important;
      color: #ffffff !important;
      line-height: 96rpx !important;
      transition: all 0.3s ease;
      box-shadow: 0 6rpx 24rpx rgba(189, 138, 79, 0.3);

      // 重置 Button 组件默认样式
      &::after {
        border: none !important;
      }

      &:not([disabled]):active {
        transform: scale(0.98);
        box-shadow: 0 3rpx 12rpx rgba(189, 138, 79, 0.2);
      }

      &[disabled] {
        background: #e9ecef !important;
        color: #999999 !important;
        box-shadow: none !important;
        opacity: 1 !important;

        &:active {
          transform: none !important;
        }

        &:not(.button-loading) {
          background: #e9ecef !important;
          color: #999999 !important;
        }
      }

      // 加载状态样式
      &.button-loading {
        background: #BD8A4F !important;
        opacity: 0.8 !important;
      }

      // Hover 效果
      &.button-hover {
        background: linear-gradient(135deg, #a67a42 0%, #c19968 100%) !important;
        transform: scale(0.98);
      }
    }
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .publish-dynamic {
    .content-area {
      padding: 24rpx;
    }

    .form-section {
      padding: 24rpx;
      margin-bottom: 20rpx;
      border-radius: 16rpx;

      .form-label {
        font-size: 30rpx;
        margin-bottom: 20rpx;

        .word-count {
          font-size: 22rpx;
        }
      }

      .category-picker {
        padding: 20rpx 24rpx;
        border-radius: 12rpx;

        .picker-text {
          font-size: 28rpx;
        }

        .picker-arrow {
          font-size: 30rpx;
        }
      }

      .content-textarea {
        padding: 20rpx;
        font-size: 28rpx;
        border-radius: 12rpx;
      }
    }

    .submit-section {
      padding: 20rpx 24rpx;
      padding-bottom: calc(20rpx + env(safe-area-inset-bottom));

      .submit-btn {
        height: 88rpx !important;
        font-size: 30rpx !important;
        border-radius: 44rpx !important;
        line-height: 88rpx !important;
      }
    }
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .publish-dynamic {
    background: #1a1a1a;

    .form-section {
      background: #2a2a2a;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);

      .form-label {
        color: #ffffff;

        .word-count {
          color: #cccccc;
        }
      }

      .category-picker {
        background: #3a3a3a;
        border-color: #4a4a4a;

        &:active {
          background: #4a4a4a;
        }

        .picker-text {
          color: #ffffff;
        }

        .picker-arrow {
          color: #cccccc;
        }
      }

      .content-textarea {
        background: #3a3a3a;
        border-color: #4a4a4a;
        color: #ffffff;

        &:focus {
          background: #2a2a2a;
          box-shadow: 0 0 0 4rpx rgba(189, 138, 79, 0.2);
        }
      }
    }

    .submit-section {
      background: #2a2a2a;
      border-top-color: #4a4a4a;

      .submit-btn {
        &[disabled]:not(.button-loading) {
          background: #4a4a4a !important;
          color: #666666 !important;
        }

        &.button-hover {
          background: linear-gradient(135deg, #a67a42 0%, #c19968 100%) !important;
        }
      }
    }

    .loading-container .loading-text {
      color: #cccccc;
    }
  }
}
