/**
 * HomeNav 首页导航组件样式
 */

.home-nav {
  margin: 30rpx;
  
  // 容器
  &__container {
    display: grid;
    grid-template-columns: repeat(4, 1fr); // 4列
    grid-template-rows: repeat(2, 1fr);    // 2行
    gap: 30rpx; // 元素间距
  }
  
  // 导航项
  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    
    // 点击效果
    &:active {
      transform: scale(0.95);
      opacity: 0.8;
    }
    
    // 无链接状态
    &--no-link {
      cursor: default;
      
      &:active {
        transform: none;
        opacity: 1;
      }
    }
  }
  
  // 图标包装器
  &__icon-wrapper {
    width: 145rpx;
    height: 145rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16rpx;
  }
  
  // 图标
  &__icon {
    width: 100%;
    height: 100%;
    display: block;
  }
  
  // 标题
  &__title {
    font-weight: 600;
    font-size: 28rpx;
    color: #000000;
    line-height: 32rpx;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 145rpx; // 与图标宽度保持一致
  }
  
  // 禁用状态
  &--disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}

// 加载状态
.home-nav--loading {
  .home-nav__icon {
    opacity: 0.3;
    animation: nav-loading 1.5s ease-in-out infinite;
  }
}

@keyframes nav-loading {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
}

// 错误状态
.home-nav--error {
  .home-nav__icon {
    filter: grayscale(100%);
    opacity: 0.5;
  }
  
  .home-nav__title {
    color: #999999;
  }
}
