/**
 * 页面状态组件
 * 
 * 包含加载中、空状态、错误状态等通用页面状态
 */
import React from 'react'
import { View, Text, Button, Image } from '@tarojs/components'

// 加载状态组件
interface PageLoadingProps {
  text?: string
}

export const PageLoading: React.FC<PageLoadingProps> = ({ 
  text = '加载中...' 
}) => {
  return (
    <View className="page-loading">
      <View className="loading-spinner" />
      <Text className="page-loading__text">{text}</Text>
    </View>
  )
}

// 空状态组件
interface PageEmptyProps {
  icon?: string
  text?: string
  description?: string
  actionText?: string
  onAction?: () => void
}

export const PageEmpty: React.FC<PageEmptyProps> = ({
  icon = '/assets/images/empty.png',
  text = '暂无数据',
  description,
  actionText,
  onAction
}) => {
  return (
    <View className="page-empty">
      <Image 
        className="page-empty__icon" 
        src={icon} 
        mode="aspectFit"
      />
      <Text className="page-empty__text">{text}</Text>
      {description && (
        <Text className="page-empty__desc">{description}</Text>
      )}
      {actionText && onAction && (
        <Button 
          className="page-empty__action"
          onClick={onAction}
        >
          {actionText}
        </Button>
      )}
    </View>
  )
}

// 错误状态组件
interface PageErrorProps {
  icon?: string
  text?: string
  description?: string
  retryText?: string
  onRetry?: () => void
}

export const PageError: React.FC<PageErrorProps> = ({
  icon = '/assets/images/error.png',
  text = '加载失败',
  description = '网络连接异常，请检查网络后重试',
  retryText = '重新加载',
  onRetry
}) => {
  return (
    <View className="page-error">
      <Image 
        className="page-error__icon" 
        src={icon} 
        mode="aspectFit"
      />
      <Text className="page-error__text">{text}</Text>
      {description && (
        <Text className="page-error__desc">{description}</Text>
      )}
      {onRetry && (
        <Button 
          className="page-error__retry"
          onClick={onRetry}
        >
          {retryText}
        </Button>
      )}
    </View>
  )
}

// 加载更多组件
interface PageLoadMoreProps {
  loading?: boolean
  hasMore?: boolean
  loadingText?: string
  noMoreText?: string
  onLoadMore?: () => void
}

export const PageLoadMore: React.FC<PageLoadMoreProps> = ({
  loading = false,
  hasMore = true,
  loadingText = '加载中...',
  noMoreText = '没有更多了',
  onLoadMore
}) => {
  const handleLoadMore = () => {
    if (!loading && hasMore && onLoadMore) {
      onLoadMore()
    }
  }

  return (
    <View 
      className={`page-load-more ${!hasMore ? 'page-load-more--no-more' : ''}`}
      onClick={handleLoadMore}
    >
      {loading && <View className="loading-spinner" />}
      <Text className="page-load-more__text">
        {loading ? loadingText : hasMore ? '点击加载更多' : noMoreText}
      </Text>
    </View>
  )
}

// 页面内容容器组件
interface PageContentProps {
  children: React.ReactNode
  className?: string
  // 便捷的内边距设置
  padded?: boolean | 'sm' | 'lg' | 'x' | 'y' | 'b'
}

export const PageContent: React.FC<PageContentProps> = ({
  children,
  className = '',
  padded = false
}) => {
  // 根据 padded 属性生成样式类名
  const getPaddedClassName = () => {
    if (!padded) return ''
    if (padded === true) return 'page-content--padded'
    return `page-content--padded-${padded}`
  }

  return (
    <View className={`page-content ${getPaddedClassName()} ${className}`}>
      {children}
    </View>
  )
}

// 页面卡片组件
interface PageCardProps {
  children: React.ReactNode
  className?: string
  title?: string
  extra?: React.ReactNode
}

export const PageCard: React.FC<PageCardProps> = ({
  children,
  className = '',
  title,
  extra
}) => {
  return (
    <View className={`page-content__card ${className}`}>
      {(title || extra) && (
        <View className="page-card__header">
          {title && <Text className="page-card__title">{title}</Text>}
          {extra && <View className="page-card__extra">{extra}</View>}
        </View>
      )}
      <View className="page-card__body">
        {children}
      </View>
    </View>
  )
}

// 页面列表组件
interface PageListProps {
  children: React.ReactNode
  className?: string
}

export const PageList: React.FC<PageListProps> = ({
  children,
  className = ''
}) => {
  return (
    <View className={`page-content__list ${className}`}>
      {children}
    </View>
  )
}
