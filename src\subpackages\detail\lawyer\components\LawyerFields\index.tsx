/**
 * 律师擅长领域组件
 * 
 * 展示律师的擅长领域标签
 */
import React from 'react'
import { View, Text } from '@tarojs/components'
import './index.scss'

// 组件属性
export interface LawyerFieldsProps {
  // 擅长领域列表
  lawyerField: LawyerAPI.LawyerFieldDto[]
  // 自定义标题
  title?: string
  // 自定义样式类名
  className?: string
  // 最大显示数量（超出显示"更多"）
  maxDisplay?: number
}

const LawyerFields: React.FC<LawyerFieldsProps> = ({
  lawyerField,
  title = '擅长领域',
  className = '',
  maxDisplay
}) => {
  // 显示的领域列表
  const displayFields = maxDisplay ? lawyerField.slice(0, maxDisplay) : lawyerField
  const hasMore = maxDisplay && lawyerField.length > maxDisplay

  return (
    <View className={`lawyer-fields ${className}`}>
      <Text className='lawyer-fields__title'>{title}</Text>
      <View className='lawyer-fields__list'>
        {displayFields.map((field) => (
          <View key={field.id} className='lawyer-fields__tag'>
            <Text className='lawyer-fields__text'>{field.name}</Text>
          </View>
        ))}
        {hasMore && (
          <View className='lawyer-fields__tag lawyer-fields__tag--more'>
            <Text className='lawyer-fields__text'>+{lawyerField.length - maxDisplay!}更多</Text>
          </View>
        )}
      </View>
    </View>
  )
}

export default LawyerFields
