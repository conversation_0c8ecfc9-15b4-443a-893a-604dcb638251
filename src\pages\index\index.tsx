import React from 'react'
import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import PageLayout, { PageContent } from '@/components/PageLayout'
import Banner, { BannerItem } from '@/components/Banner'
import LawyerList from '@/components/LawyerList'
import HorizontalCaseList from '@/components/HorizontalCaseList'
import Service from '@/components/Service'

import Logo from '@/assets/images/logo.png'
import HomeBg from '@/assets/images/home_bg.png'
import BookMark from '@/assets/images/common-icon/book_mark_color.png'
import VerifyIcon from '@/assets/images/common-icon/verify.png'

import HomeNav from './components/HomeNav'
import './index.scss'

const Index: React.FC = () => {
  const banners: BannerItem[] = [
    {
      id: 'home-banner-1',
      imageUrl: HomeBg
    }
  ]
  const onClickMoreLawyer = () => {
    Taro.switchTab({
      url: '/pages/lawyer/index'
    })
  }

  const onClickMoreCase = () => {
    Taro.switchTab({
      url: '/pages/case/index'
    })
  }

  return (
    <PageLayout
      showLogo
      showBack={false}
      logoSrc={Logo}
      backgroundColor='#f8f9fa'
    >
      <PageContent padded='b'>
        <Banner
          banners={banners}
          autoplay={false}
          showIndicators={false}
          showDots={false}
          circular={false}
          className='home-banner'
        />
        <HomeNav />
        <View className='home-content-padded home-lawyer-list'>
          <View className='home-lawyer-list__header'>
            <View className='home-lawyer-list__title'>
              <Image className='home-lawyer-list__title-icon' src={VerifyIcon} mode='aspectFit' />
              <Text className='home-lawyer-list__title-text'>品牌律师</Text>
            </View>
            <View className='home-lawyer-list__more' onClick={onClickMoreLawyer}>
              <Text>更多</Text>
            </View>
          </View>
          <LawyerList />
        </View>
        <View className='home-case-list'>
          <View className='home-case-list__header'>
            <View className='home-case-list__title'>
              <Image className='home-case-list__title-icon' src={BookMark} mode='aspectFit' />
              <Text className='home-case-list__title-text'>最新亲办案例</Text>
            </View>
            <View className='home-case-list__more' onClick={onClickMoreCase}>
              <Text>更多</Text>
            </View>
          </View>
          <HorizontalCaseList />
        </View>
        <Service />
      </PageContent>
    </PageLayout>
  )
}

export default Index
