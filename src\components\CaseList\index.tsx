/**
 * 纵向案例组件
 * 
 * 展示案例列表，支持外部传入筛选条件，纵向布局
 */
import React, { useState, useEffect } from 'react'
import { View, Text, Image } from '@tarojs/components'
import { caseApi } from '@/utils/request/apis'
import { navigateToPage } from '@/utils'
import ClockIcon from '@/assets/images/common-icon/clock.png'
import { CaseListProps } from './types'
import './index.scss'

const CaseList: React.FC<CaseListProps> = ({
  filterParams,
  className = ''
}) => {
  // 案例列表数据
  const [cases, setCases] = useState<CaseAPI.CaseListInfo[]>([])
  // 加载状态
  const [isLoading, setIsLoading] = useState(false)

  // 加载案例列表数据
  const loadCases = async () => {
    try {
      setIsLoading(true)

      const params = {
        ...filterParams,
        page: 1,
        pageSize: 10 // 纵向列表可以显示更多案例
      }

      const response = await caseApi.getCaseList(params)
      if (response.success && response.data) {
        setCases(response.data.list)
      }
    } catch (error) {
      console.error('加载案例列表失败:', error)
      // 模拟数据作为fallback
      const mockList: CaseAPI.CaseListInfo[] = [
        {
          id: 1,
          title: "劳动合同纠纷案例分析：员工被无故辞退如何维权",
          categoryId: 1,
          categoryName: "劳动纠纷",
          viewCount: 1250,
          creator: "张律师",
          createdAt: "2025-06-20 10:00:00"
        },
        {
          id: 2,
          title: "房产买卖合同纠纷处理实务：购房者如何保护自己的权益",
          categoryId: 2,
          categoryName: "房产纠纷",
          viewCount: 980,
          creator: "李律师",
          createdAt: "2025-06-19 09:15:00"
        },
        {
          id: 3,
          title: "交通事故责任认定与赔偿标准详解",
          categoryId: 3,
          categoryName: "交通事故",
          viewCount: 2100,
          creator: "王律师",
          createdAt: "2025-06-18 08:30:00"
        },
        {
          id: 4,
          title: "婚姻家庭纠纷中的财产分割原则与实践",
          categoryId: 4,
          categoryName: "婚姻家庭",
          viewCount: 1680,
          creator: "陈律师",
          createdAt: "2025-06-17 07:20:00"
        },
        {
          id: 5,
          title: "公司股权转让纠纷案例分析及风险防范",
          categoryId: 5,
          categoryName: "公司法务",
          viewCount: 890,
          creator: "刘律师",
          createdAt: "2025-06-16 06:10:00"
        },
        {
          id: 6,
          title: "知识产权侵权纠纷的法律适用与实务操作",
          categoryId: 6,
          categoryName: "知识产权",
          viewCount: 1420,
          creator: "赵律师",
          createdAt: "2025-06-15 05:45:00"
        }
      ]
      setCases(mockList)
      console.error('加载案例列表失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 处理案例卡片点击 - 跳转到案例详情页
  const handleCaseClick = (caseInfo: CaseAPI.CaseListInfo) => {
    navigateToPage(`/subpackages/detail/case/index?id=${caseInfo.id}`)
  }

  // 组件挂载时加载数据
  useEffect(() => {
    loadCases()
  }, [filterParams])

  // 监听筛选参数变化，重新加载数据
  useEffect(() => {
    if (filterParams) {
      loadCases()
    }
  }, [filterParams])

  // 显示加载状态
  const showLoading = isLoading

  return (
    <View className={`case-list ${className}`}>
      {showLoading ? (
        <View className='case-list__loading'>
          <Text>加载中...</Text>
        </View>
      ) : (
        <>
          {cases.length > 0 ? (
            <View className='case-list__container'>
              {cases.map((caseInfo) => (
                <View
                  key={caseInfo.id}
                  className='case-item'
                  onClick={() => handleCaseClick(caseInfo)}
                >
                  <Text className='case-item__title'>{caseInfo.title}</Text>
                  <View className='case-item__meta'>
                    <Text className='case-item__category'>{caseInfo.categoryName}</Text>
                    <Image src={ClockIcon} mode='aspectFit' className='case-item__clock-icon' />
                    <Text className='case-item__time'>{caseInfo.createdAt}</Text>
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <View className='case-list__empty'>
              <Text className='case-list__empty-title'>暂无案例信息</Text>
              <Text className='case-list__empty-desc'>当前筛选条件下没有找到相关案例，请尝试调整搜索条件</Text>
            </View>
          )}
        </>
      )}
    </View>
  )
}

// 导出组件
export default CaseList

// 导出类型
export type { CaseListProps } from './types'
