/**
 * 我的页面样式
 */

// 页面容器
.mine-page {
  position: relative;
  min-height: 100vh;
}

// 固定背景图片
.mine-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--taro-nav-height, 176rpx) + 280rpx); // 导航栏高度 + 280rpx
  background-image: url('../../assets/images/user_bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: -1;
}

.mine-content {

  // 用户信息区域
  .user-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 280rpx;
    width: 750rpx;
    padding: 57rpx 30rpx;
    background: transparent;
    box-sizing: border-box;

    // 左侧用户信息模块
    .user-info-module {
      display: flex;
      align-items: center;

      .user-avatar {
        width: 166rpx;
        height: 166rpx;
        border-radius: 50%;
        background: #dbdada;
        border: 2rpx solid #ffffff;
        margin-right: 20rpx;
        overflow: hidden;

        &:active {
          opacity: 0.8;
        }

        .avatar-image {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }

      .user-info {
        display: flex;
        flex-direction: column;
        justify-content: center;

        .user-name-level {
          display: flex;
          align-items: center;
          margin-bottom: 8rpx;

          .user-name {
            font-weight: 600;
            font-size: 32rpx;
            color: #000000;
            line-height: 48rpx;
            margin-right: 16rpx;
          }

          .lawyer-level-icon {
            width: 120rpx;
            height: 40rpx;
            flex-shrink: 0;
          }
        }

        .user-location {
          font-weight: 600;
          font-size: 24rpx;
          color: #000000;
          line-height: 33rpx;
          margin-bottom: 4rpx;
        }

        .user-firm {
          font-weight: 600;
          font-size: 24rpx;
          color: #000000;
          line-height: 33rpx;
        }

        // 未登录状态提示语
        .user-desc {
          font-weight: 400;
          font-size: 24rpx;
          color: #000;
          line-height: 33rpx;
        }
      }
    }

    // 右侧消息提示图标
    .message-icon {
      position: relative;
      width: 64rpx;
      height: 64rpx;
      border: 1rpx solid #000000;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .chat-icon {
        width: 44rpx;
        height: 44rpx;
      }

      .message-badge {
        position: absolute;
        top: -18rpx;
        right: -8rpx;
        min-width: 32rpx;
        height: 32rpx;
        background: #ff4444;
        color: #ffffff;
        font-size: 20rpx;
        font-weight: 600;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 8rpx;
        line-height: 1;
      }
    }
  }

  // 菜单区域
  .menu-section {
    padding: 0 25rpx;

    .menu-module {
      margin-top: 30rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .module-title {
        font-weight: 600;
        font-size: 32rpx;
        color: #BD8A4F;
        margin-left: 20rpx;
        margin-bottom: 20rpx;
      }

      .module-content {
        width: 700rpx;
        height: 226rpx;
        background: #FFFFFF;
        box-shadow: 0rpx 2rpx 48rpx 0rpx rgba(0, 0, 0, 0.04);
        border-radius: 20rpx;
        padding: 50rpx 40rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;

        .menu-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          &:active {
            opacity: 0.8;
          }

          .menu-icon {
            width: 64rpx;
            height: 64rpx;
            margin-bottom: 20rpx;
          }

          .menu-text {
            font-weight: 400;
            font-size: 24rpx;
            color: #727E8C;
            text-align: center;
          }
        }
      }
    }
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .mine-content {
    .menu-section {
      .menu-module {
        .module-content {
          width: 100%;
          padding: 40rpx 30rpx;

          .menu-item {
            .menu-icon {
              width: 56rpx;
              height: 56rpx;
              margin-bottom: 16rpx;
            }

            .menu-text {
              font-size: 22rpx;
            }
          }
        }
      }
    }
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .mine-content {
    .menu-section {
      .menu-module {
        .module-content {
          background: #1a1a1a;

          .menu-item {
            .menu-text {
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}