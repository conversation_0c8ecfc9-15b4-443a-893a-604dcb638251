# API接口更新总结

根据api.json接口文档，已完成对src\utils\request\apis接口代码和类型的全面更新。

## 更新内容概览

### 1. 动态API模块 (dynamics/)

#### 新增类型定义
- `LawyerDynamicsCategoryCountDto` - 律师动态分类统计类型
- `OwnerCenterDynamicsListInfo` - 律师个人中心动态列表信息
- `GetRecommendDynamicsListRequest/Response` - 获取推荐动态列表
- `GetOwnerCenterDynamicsListRequest/Response` - 获取律师个人中心动态列表
- `CollectArticleRequest/Response` - 文章收藏
- `LikeArticleRequest/Response` - 文章点赞

#### 新增接口方法
```typescript
// 获取推荐动态列表
getRecommendDynamicsList(dynamicsId: number, params?: GetRecommendDynamicsListRequest)

// 获取律师个人中心动态列表
getOwnerCenterDynamicsList(params?: GetOwnerCenterDynamicsListRequest)

// 文章收藏
collectArticle(articleId: number)

// 文章点赞
likeArticle(articleId: number)
```

#### 更新的接口路径
- `/mini/find-law-dynamics/list` - 获取动态列表
- `/mini/find-law-dynamics/:dynamicsId/detail` - 获取动态详情
- `/mini/find-law-dynamics/:lawyerId/list` - 获取律师动态列表
- `/mini/find-law-dynamics/:dynamicsId/recommend-list` - 获取推荐动态列表
- `/mini/common/law-dynamics/category-list` - 获取动态分类列表
- `/mini/owner-center-lawyer/law-dynamics/list` - 获取律师个人中心动态列表
- `/mini/auth-law-article/:articleId/collect` - 文章收藏
- `/mini/auth-law-article/:articleId/nice` - 文章点赞

### 2. 律师API模块 (lawyer/)

#### 新增类型定义
- `GetLawyerDetailInfoRequest/Response` - 获取律师详情信息
- `GetLawyerCountDataRequest/Response` - 获取律师统计数据
- `GetLawyerContentNumRequest/Response` - 获取律师内容统计
- `FollowLawyerRequest/Response` - 关注律师
- `OwnerLawyerFollowListInfo` - 律师关注列表信息
- `GetOwnerLawyerFollowListRequest/Response` - 获取律师关注列表
- `GetOwnerLawyerInfoResponse` - 获取律师个人信息

#### 新增接口方法
```typescript
// 获取律师详情信息
getLawyerDetailInfo(lawyerId: number)

// 获取律师统计数据
getLawyerCountData(lawyerId: number)

// 获取律师内容统计
getLawyerContentNum(lawyerId: number)

// 关注律师
followLawyer(lawyerId: number)

// 获取律师关注列表
getOwnerLawyerFollowList(params?: GetOwnerLawyerFollowListRequest)

// 获取律师个人信息
getOwnerLawyerInfo()
```

#### 更新的接口路径
- `/mini/find-lawyer/list` - 获取律师列表
- `/mini/find-lawyer/:lawyerId/detail-info` - 获取律师详情信息
- `/mini/find-lawyer/:lawyerId/detail-lawyer-count` - 获取律师统计数据
- `/mini/find-lawyer/:lawyerId/detail-content-count` - 获取律师内容统计
- `/mini/find-lawyer/:lawyerId/follow` - 关注律师
- `/mini/owner-center-lawyer/lawyer-follow/list` - 获取律师关注列表
- `/mini/owner-center-lawyer/lawyer-info` - 获取律师个人信息

### 3. 案例API模块 (case/)

#### 新增类型定义
- `GetRecommendCaseListRequest/Response` - 获取推荐案例列表
- `OwnerCenterCaseListInfo` - 律师个人中心案例列表信息
- `GetOwnerCenterCaseListRequest/Response` - 获取律师个人中心案例列表
- `CaseStageInfo` - 案件阶段类型
- `GetCaseStageListResponse` - 获取案件阶段列表

#### 新增接口方法
```typescript
// 获取推荐案例列表
getRecommendCaseList(caseId: number, params?: GetRecommendCaseListRequest)

// 获取案件阶段列表
getCaseStageList()

// 获取律师个人中心案例列表
getOwnerCenterCaseList(params?: GetOwnerCenterCaseListRequest)
```

#### 更新的接口路径
- `/mini/find-law-case/list` - 获取案例列表
- `/mini/find-law-case/:caseId/detail` - 获取案例详情
- `/mini/find-law-case/:caseId/recommend-list` - 获取推荐案例列表
- `/mini/common/law-case/category-list` - 获取案例分类列表
- `/mini/common/law-order/case-stage-list` - 获取案件阶段列表
- `/mini/owner-center-lawyer/law-case/list` - 获取律师个人中心案例列表

## 技术改进

### 1. 类型安全增强
- 所有接口方法都添加了完整的TypeScript类型定义
- 使用Promise<BaseResponse<T>>统一返回类型
- 基于api.json文档的准确类型映射

### 2. 接口路径规范化
- 根据api.json文档更新了所有接口路径
- 统一了路径参数的处理方式
- 添加了详细的接口注释说明

### 3. 代码结构优化
- 导入了BaseResponse类型以确保类型一致性
- 添加了详细的JSDoc注释
- 保持了模块化的代码组织结构

## 使用示例

```typescript
import { dynamicsApi, lawyerApi, caseApi } from '@/utils/request/apis'

// 获取动态列表
const dynamicsResponse = await dynamicsApi.getDynamicsList({
  page: 1,
  pageSize: 10,
  categoryId: 1
})

// 获取律师详情信息
const lawyerInfo = await lawyerApi.getLawyerDetailInfo(123)

// 获取案例详情
const caseDetail = await caseApi.getCaseDetail(456)

// 关注律师
await lawyerApi.followLawyer(123)

// 收藏文章
await dynamicsApi.collectArticle(789)
```

## 注意事项

1. 所有接口都已更新为基于api.json文档的最新版本
2. 保持了向后兼容性，现有代码无需修改
3. 新增的接口方法可以直接使用
4. 建议在使用新接口时参考对应的类型定义
